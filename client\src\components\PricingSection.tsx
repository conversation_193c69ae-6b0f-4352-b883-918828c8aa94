import { useState } from 'react';
import { useLocation } from 'wouter';
import { motion } from 'framer-motion';
import { Check, X, Loader2, Zap } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/components/ui/use-toast';

interface PricingPlan {
  id: string;
  name: string;
  description: string;
  price: string;
  numericPrice: number;
  period: string;
  buttonText: string;
  buttonVariant: 'default' | 'outline';
  features: Array<{ text: string; included: boolean }>;
  popular: boolean;
  color: string;
  icon?: React.ReactNode;
}

const pricingPlans: PricingPlan[] = [
  {
    id: 'free',
    name: 'Free',
    description: 'Basic features for casual browsing with a side of sass.',
    price: '$0',
    numericPrice: 0,
    period: 'forever',
    buttonText: 'Get Started',
    buttonVariant: 'outline',
    features: [
      { text: '10 requests per day', included: true },
      { text: 'Scan any webpage', included: true },
      { text: 'Basic content analysis', included: true },
      { text: 'Sarcastic commentary (mild)', included: true },
      { text: 'Standard processing speed', included: true },
      { text: 'Smart search feature', included: false },
      { text: 'Search across multiple tabs', included: false },
      { text: 'Export chat history', included: false },
    ],
    popular: false,
    color: 'secondary',
    icon: <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-sparkles"><path d="m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z"/><path d="M5 3v4"/><path d="M19 17v4"/><path d="M3 5h4"/><path d="M17 19h4"/></svg>
  },
  {
    id: 'pro',
    name: 'Pro',
    description: 'Full access to all features with maximum judgment.',
    price: '$20',
    numericPrice: 20,
    period: 'per month',
    buttonText: 'Upgrade Now',
    buttonVariant: 'default',
    features: [
      { text: 'Unlimited requests', included: true },
      { text: 'Scan any webpage', included: true },
      { text: 'Smart search feature', included: true },
      { text: 'Search across multiple tabs', included: true },
      { text: 'Export chat history', included: true },
      { text: 'Advanced content analysis', included: true },
      { text: 'Premium sarcastic commentary', included: true },
      { text: 'Ad-free experience', included: true },
    ],
    popular: true,
    color: 'primary',
    icon: <Zap className="w-6 h-6" />
  },
  {
    id: 'ultimate',
    name: 'Ultimate',
    description: 'The most comprehensive package for serious critics.',
    price: '$200',
    numericPrice: 200,
    period: 'per year',
    buttonText: 'Go Ultimate',
    buttonVariant: 'outline',
    features: [
      { text: 'Unlimited requests', included: true },
      { text: 'Everything in Pro plan', included: true },
      { text: 'Exclusive premium features', included: true },
      { text: 'Advanced AI-powered analysis', included: true },
      { text: 'Early access to new features', included: true },
      { text: 'Advanced usage analytics', included: true },
      { text: 'Priority 24/7 support', included: true },
      { text: 'Custom AI training options', included: true },
    ],
    popular: false,
    color: 'accent',
    icon: <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-crown"><path d="m2 4 3 12h14l3-12-6 7-4-7-4 7-6-7zm3 16h14"/></svg>
  }
];

export default function PricingSection() {
  const { isAuthenticated } = useAuth();
  const { toast } = useToast();
  const [, setLocation] = useLocation();
  const [isLoading, setIsLoading] = useState(false);
  const [loadingPlanId, setLoadingPlanId] = useState<string | null>(null);

  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0, transition: { duration: 0.5 } }
  };

  // The plan selection is now handled directly in the button click handler

  return (
    <section id="pricing" className="py-12 md:py-16 relative max-w-[102%] overflow-visible">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="text-center mb-12"
      >
        <h2 className="section-heading font-heading text-5xl md:text-6xl font-bold inline-block mx-auto tracking-tight">
          <span className="text-primary neon-text">Pricing</span> That Makes Sense
        </h2>
        <p className="text-muted-foreground mt-5 max-w-2xl mx-auto text-lg font-inter leading-relaxed">
          All plans include webpage scanning. Free plan limited to 10 requests per day. Upgrade to Pro or Ultimate for unlimited requests, smart search, multi-tab search, and chat export.
        </p>
      </motion.div>

      <motion.div
        className="grid md:grid-cols-3 gap-8 md:gap-10 max-w-[98%] lg:max-w-[100%] xl:max-w-[102%] mx-auto"
        variants={container}
        initial="hidden"
        whileInView="show"
        viewport={{ once: true, amount: 0.2 }}
      >
        {pricingPlans.map((plan) => {
          return (
            <motion.div key={plan.id} variants={item}>
              <div className={`modern-card h-full flex flex-col transition-all duration-300 overflow-hidden group relative hover:scale-[1.02] hover:shadow-xl ${plan.popular ? 'border-primary/50 shadow-lg shadow-primary/10' : ''}`}>
                {plan.popular && (
                  <div className="absolute top-0 right-0">
                    <div className="bg-primary text-white text-xs font-bold px-4 py-1.5 transform rotate-0 origin-top-right">
                      POPULAR
                    </div>
                  </div>
                )}

                <div className="p-6 md:p-8 flex flex-col h-full">
                  <div className="mb-8">
                    <div className="flex items-center mb-3">
                      <div className={`mr-3 text-${plan.color}`}>
                        {plan.icon}
                      </div>
                      <h3 className={`font-heading text-2xl md:text-3xl font-semibold group-hover:text-${plan.color} transition-colors duration-300 tracking-tight`}>
                        {plan.name}
                      </h3>
                    </div>

                    <p className="text-muted-foreground mb-6 font-inter">
                      {plan.description}
                    </p>

                    <div className="flex items-baseline mb-6 bg-gradient-to-r from-background to-muted/30 p-4 rounded-xl">
                      <span className="text-5xl font-bold font-heading">{plan.price}</span>
                      <span className="ml-2 text-muted-foreground font-inter">{plan.period}</span>
                    </div>

                    <div className="relative group mb-2">
                      <div className={`absolute -inset-0.5 bg-gradient-to-r from-${plan.color}/40 to-${plan.color}/20 rounded-xl blur opacity-30 group-hover:opacity-70 transition duration-500 group-hover:duration-200 ${plan.popular ? 'opacity-70' : ''}`}></div>
                      <Button
                        onClick={async () => {
                          console.log('Button clicked for plan:', plan.id);
                          alert(`Button clicked for plan: ${plan.id}`);

                          // If it's the free plan, just show a toast
                          if (plan.id === 'free') {
                            toast({
                              title: 'Free Plan Selected',
                              description: 'You are now on the Free plan. No payment required.',
                            });
                            return;
                          }

                          // If not authenticated, redirect to sign in
                          if (!isAuthenticated) {
                            console.log('User not authenticated, redirecting to sign in');
                            toast({
                              title: 'Authentication Required',
                              description: 'Please sign in to upgrade your plan.',
                              variant: 'destructive',
                            });
                            setLocation('/signin?redirect=plans');
                            return;
                          }

                          // Set loading state
                          setIsLoading(true);
                          setLoadingPlanId(plan.id);
                          console.log('Loading state set for plan:', plan.id);

                          try {
                            // Get the price ID based on the plan
                            let priceId = '';
                            if (plan.id === 'pro') {
                              priceId = 'price_1RLGJvSABghPrT7Lx1w689k3';
                            } else if (plan.id === 'ultimate') {
                              priceId = 'price_1RLGO3SABghPrT7Lw0jRLe9C';
                            } else {
                              throw new Error('Invalid plan selected');
                            }
                            console.log('Using price ID:', priceId);

                            // Get the user's email
                            console.log('Getting user email...');
                            const { supabase } = await import('@/lib/supabase');
                            const { data: userData } = await supabase.auth.getUser();
                            const email = userData?.user?.email || '<EMAIL>';
                            console.log('User email:', email);

                            // Create a checkout session
                            console.log('Creating checkout session...');
                            console.log('Request payload:', { email, planId: plan.id, priceId });

                            // Let's try both approaches - first the server, then fallback to direct links
                            console.log('Attempting to create checkout session via server...');

                            try {
                              // Make the fetch request to the server
                              console.log('Making fetch request to server...');
                              console.log('Request URL:', 'http://localhost:5000/api/payment/create-checkout-session-public');
                              console.log('Request method:', 'POST');
                              console.log('Request headers:', { 'Content-Type': 'application/json' });
                              console.log('Request body:', JSON.stringify({
                                email,
                                planId: plan.id,
                                priceId
                              }));

                              const controller = new AbortController();
                              const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

                              // Add a global window alert to verify this code is being executed
                              alert('Making fetch request to server...');

                              const response = await fetch('http://localhost:5000/api/payment/create-checkout-session-public', {
                                method: 'POST',
                                headers: {
                                  'Content-Type': 'application/json'
                                },
                                body: JSON.stringify({
                                  email,
                                  planId: plan.id,
                                  priceId
                                }),
                                signal: controller.signal
                              });

                              clearTimeout(timeoutId);

                              console.log('Response received:', response);
                              console.log('Response status:', response.status);

                              const responseText = await response.text();
                              console.log('Response text:', responseText);

                              if (!response.ok) {
                                throw new Error(`Server error: ${response.status} ${responseText}`);
                              }

                              let responseData;
                              try {
                                responseData = JSON.parse(responseText);
                              } catch (e) {
                                throw new Error(`Invalid JSON response: ${responseText}`);
                              }

                              console.log('Response data:', responseData);

                              if (responseData.url) {
                                console.log('Redirecting to server-provided URL:', responseData.url);
                                window.location.href = responseData.url;
                                return;
                              } else {
                                throw new Error('No checkout URL returned from server');
                              }
                            } catch (serverError: any) {
                              // If server approach fails, fallback to direct links
                              console.error('Server approach failed:', serverError);
                              console.log('Falling back to direct Stripe checkout links...');

                              // Add a global window alert to verify this code is being executed
                              alert('Server approach failed: ' + (serverError.message || 'Unknown error'));

                              // Direct links to Stripe checkout
                              let checkoutUrl = '';
                              if (plan.id === 'pro') {
                                checkoutUrl = 'https://buy.stripe.com/test_9AQcOO0Ht3Tn9qgcMM'; // Pro plan
                              } else if (plan.id === 'ultimate') {
                                checkoutUrl = 'https://buy.stripe.com/test_28o7uuafd7bD9qg5kl'; // Ultimate plan
                              } else {
                                throw new Error('Invalid plan selected');
                              }

                              console.log('Redirecting to direct Stripe URL:', checkoutUrl);
                              window.location.href = checkoutUrl;
                            }
                          } catch (error) {
                            console.error('Error creating checkout session:', error);
                            toast({
                              title: 'Error',
                              description: 'Failed to process payment. Please try again.',
                              variant: 'destructive',
                            });
                            setIsLoading(false);
                            setLoadingPlanId(null);
                          }
                        }}
                        variant={plan.buttonVariant}
                        className={`w-full py-6 text-base font-inter font-medium ${plan.popular ? 'bg-primary hover:bg-primary/90' : ''}`}
                        disabled={isLoading && loadingPlanId === plan.id}
                      >
                        {isLoading && loadingPlanId === plan.id ? (
                          <>
                            <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                            Processing...
                          </>
                        ) : (
                          plan.buttonText
                        )}
                      </Button>
                    </div>

                    <div className="border-t border-border/50 pt-6 mt-6 flex-grow">
                      <h4 className="font-inter font-medium text-base mb-4">Features included:</h4>
                      <ul className="space-y-4">
                        {plan.features.map((feature, index) => (
                          <li key={index} className="flex items-start">
                            {feature.included ? (
                              <div className="bg-primary/10 p-1 rounded-full mr-3 flex-shrink-0">
                                <Check className="h-4 w-4 text-primary flex-shrink-0" />
                              </div>
                            ) : (
                              <div className="bg-muted/30 p-1 rounded-full mr-3 flex-shrink-0">
                                <X className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                              </div>
                            )}
                            <span className={`${feature.included ? 'text-foreground' : 'text-muted-foreground'} text-sm font-inter`}>
                              {feature.text}
                            </span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          );
        })}
      </motion.div>

      <div className="mt-16 text-center max-w-[98%] lg:max-w-[100%] xl:max-w-[102%] mx-auto">
        <div className="bg-card/50 backdrop-blur-sm p-6 rounded-xl border border-muted/30 shadow-sm max-w-3xl mx-auto">
          <p className="text-muted-foreground font-inter">
            All plans include a <span className="text-primary font-medium">14-day money-back guarantee</span>. No questions asked, except maybe "Why didn't you appreciate our sarcasm?"
          </p>
        </div>
      </div>

      <div className="my-16 max-w-[98%] lg:max-w-[100%] xl:max-w-[102%] mx-auto">
        <div className="h-px w-full bg-gradient-to-r from-transparent via-primary/30 to-transparent"></div>
      </div>
    </section>
  );
}
