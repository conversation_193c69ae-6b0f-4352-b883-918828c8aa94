import { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { useLocation } from 'wouter';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { PageHeader } from '@/components/PageHeader';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent } from '@/components/ui/card';
import {
  Download,
  Settings,
  Code,
  Key,
  Search,
  MessageSquare,
  Zap,
  HelpCircle,
  FileText,
  Sparkles
} from 'lucide-react';

export default function Documentation() {
  const [location] = useLocation();
  const [activeTab, setActiveTab] = useState('getting-started');

  useEffect(() => {
    // Update document title
    document.title = "Documentation - browzy - Supercharge Your Browser with AI";

    // Add meta description
    const metaDescription = document.createElement('meta');
    metaDescription.name = 'description';
    metaDescription.content = 'Browzy AI Documentation - Learn how to install, configure, and use the Browzy AI browser extension to supercharge your browsing experience.';
    document.head.appendChild(metaDescription);

    // Check for tab parameter in URL
    const urlParams = new URLSearchParams(window.location.search);
    const tabParam = urlParams.get('tab');

    // Set active tab based on URL parameter if it exists and is valid
    if (tabParam && ['getting-started', 'configuration', 'api', 'features', 'faq'].includes(tabParam)) {
      setActiveTab(tabParam);
    }

    window.scrollTo(0, 0);

    return () => {
      document.head.removeChild(metaDescription);
    };
  }, [location]);

  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0, transition: { duration: 0.5 } }
  };

  return (
    <div className="min-h-screen dark:bg-black light:bg-white text-foreground overflow-hidden">
      <Header />

      <PageHeader
        title="Documentation"
        description="Everything you need to know about using Browzy AI"
      />

      <div className="max-w-6xl mx-auto px-6 sm:px-8 lg:px-12 py-12">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid grid-cols-2 md:grid-cols-5 gap-2 mb-8">
            <TabsTrigger value="getting-started" className="font-inter">
              <Download className="w-4 h-4 mr-2" />
              Getting Started
            </TabsTrigger>
            <TabsTrigger value="configuration" className="font-inter">
              <Settings className="w-4 h-4 mr-2" />
              Configuration
            </TabsTrigger>
            <TabsTrigger value="api" className="font-inter">
              <Code className="w-4 h-4 mr-2" />
              API
            </TabsTrigger>
            <TabsTrigger value="features" className="font-inter">
              <Sparkles className="w-4 h-4 mr-2" />
              Features
            </TabsTrigger>
            <TabsTrigger value="faq" className="font-inter">
              <HelpCircle className="w-4 h-4 mr-2" />
              FAQ
            </TabsTrigger>
          </TabsList>

          <TabsContent value="getting-started">
            <motion.div
              variants={container}
              initial="hidden"
              animate="show"
              className="prose dark:prose-invert prose-lg max-w-none font-inter"
            >
              <motion.section variants={item} className="mb-10">
                <h2 className="text-2xl font-bold mb-4">Installation</h2>
                <p className="text-muted-foreground mb-6">
                  Browzy AI is available as a browser extension for Chrome and other Chromium-based browsers.
                </p>

                <Card className="mb-6">
                  <CardContent className="pt-6">
                    <h3 className="text-xl font-semibold mb-3">Chrome Web Store</h3>
                    <ol className="list-decimal pl-6 text-muted-foreground">
                      <li className="mb-2">Visit our <a href="https://chromewebstore.google.com/detail/browzyai/nfjipaniijjigbomgmnciigeikblnfmk" className="text-primary hover:underline">Chrome Web Store page</a></li>
                      <li className="mb-2">Click "Add to Chrome" to install the extension</li>
                      <li className="mb-2">Click the "Add to Chrome" button</li>
                      <li>Confirm the installation when prompted</li>
                    </ol>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="pt-6">
                    <h3 className="text-xl font-semibold mb-3">Manual Installation</h3>
                    <ol className="list-decimal pl-6 text-muted-foreground">
                      <li className="mb-2">Download the latest release from our <a href="https://github.com/browzyai/extension/releases" className="text-primary hover:underline">GitHub repository</a></li>
                      <li className="mb-2">Unzip the downloaded file</li>
                      <li className="mb-2">Open Chrome and navigate to <code>chrome://extensions</code></li>
                      <li className="mb-2">Enable "Developer mode" using the toggle in the top-right corner</li>
                      <li className="mb-2">Click "Load unpacked" and select the unzipped folder</li>
                      <li>The extension should now be installed and visible in your browser toolbar</li>
                    </ol>
                  </CardContent>
                </Card>
              </motion.section>

              <motion.section variants={item} className="mb-10">
                <h2 className="text-2xl font-bold mb-4">Creating an Account</h2>
                <p className="text-muted-foreground mb-6">
                  To use Browzy AI, you'll need to create an account. This allows us to track your usage, provide personalized features, and manage your subscription.
                </p>

                <ol className="list-decimal pl-6 text-muted-foreground">
                  <li className="mb-2">Click on the Browzy AI icon in your browser toolbar</li>
                  <li className="mb-2">Click "Sign Up" in the extension popup</li>
                  <li className="mb-2">Enter your email address and create a password</li>
                  <li className="mb-2">Alternatively, you can sign up with your Google account</li>
                  <li>Once registered, you'll be automatically logged in</li>
                </ol>
              </motion.section>
            </motion.div>
          </TabsContent>

          <TabsContent value="configuration">
            <motion.div
              variants={container}
              initial="hidden"
              animate="show"
              className="prose dark:prose-invert prose-lg max-w-none font-inter"
            >
              <motion.section variants={item} className="mb-10">
                <h2 className="text-2xl font-bold mb-4">API Keys</h2>
                <p className="text-muted-foreground mb-6">
                  Browzy AI uses OpenRouter to connect to various AI models. You'll need to set up your API key to use the extension.
                </p>

                <Card className="mb-6">
                  <CardContent className="pt-6">
                    <h3 className="text-xl font-semibold mb-3">Setting Up OpenRouter</h3>
                    <ol className="list-decimal pl-6 text-muted-foreground">
                      <li className="mb-2">Create an account at <a href="https://openrouter.ai" className="text-primary hover:underline">OpenRouter.ai</a></li>
                      <li className="mb-2">Navigate to your API Keys section</li>
                      <li className="mb-2">Create a new API key for Browzy AI</li>
                      <li className="mb-2">Copy the API key</li>
                    </ol>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="pt-6">
                    <h3 className="text-xl font-semibold mb-3">Configuring Browzy AI</h3>
                    <ol className="list-decimal pl-6 text-muted-foreground">
                      <li className="mb-2">Open the Browzy AI extension</li>
                      <li className="mb-2">Click on the Settings icon</li>
                      <li className="mb-2">Navigate to the "API Keys" section</li>
                      <li className="mb-2">Paste your OpenRouter API key</li>
                      <li>Click "Save" to apply the changes</li>
                    </ol>
                  </CardContent>
                </Card>
              </motion.section>

              <motion.section variants={item} className="mb-10">
                <h2 className="text-2xl font-bold mb-4">Preferences</h2>
                <p className="text-muted-foreground mb-6">
                  Customize Browzy AI to match your preferences and browsing habits.
                </p>

                <h3 className="text-xl font-semibold mb-3">General Settings</h3>
                <ul className="list-disc pl-6 text-muted-foreground mb-6">
                  <li className="mb-2"><strong>Theme:</strong> Choose between light, dark, or system theme</li>
                  <li className="mb-2"><strong>Startup Behavior:</strong> Configure whether Browzy AI should activate automatically when you open a new tab</li>
                  <li className="mb-2"><strong>Keyboard Shortcuts:</strong> Customize keyboard shortcuts for quick access to Browzy AI features</li>
                  <li><strong>Notifications:</strong> Control when and how Browzy AI notifies you</li>
                </ul>

                <h3 className="text-xl font-semibold mb-3">AI Model Settings</h3>
                <ul className="list-disc pl-6 text-muted-foreground">
                  <li className="mb-2"><strong>Default Model:</strong> Select your preferred AI model for general queries</li>
                  <li className="mb-2"><strong>Response Length:</strong> Adjust the verbosity of AI responses</li>
                  <li className="mb-2"><strong>Sarcasm Level:</strong> Control how sarcastic Browzy AI should be in its responses</li>
                  <li><strong>Memory:</strong> Decide whether Browzy AI should remember your previous interactions within a session</li>
                </ul>
              </motion.section>
            </motion.div>
          </TabsContent>

          <TabsContent value="api">
            <motion.div
              variants={container}
              initial="hidden"
              animate="show"
              className="prose dark:prose-invert prose-lg max-w-none font-inter"
            >
              <motion.section variants={item} className="mb-10">
                <h2 className="text-2xl font-bold mb-4">API Reference</h2>
                <p className="text-muted-foreground mb-6">
                  Browzy AI provides a JavaScript API that allows you to integrate with the extension programmatically.
                </p>

                <Card className="mb-6">
                  <CardContent className="pt-6">
                    <h3 className="text-xl font-semibold mb-3">Initialization</h3>
                    <pre className="bg-muted p-4 rounded-md overflow-x-auto">
                      <code>
{`// Initialize Browzy AI
window.browzyAI.init({
  apiKey: 'your-api-key',
  defaultModel: 'gpt-4',
  debug: false
});`}
                      </code>
                    </pre>
                  </CardContent>
                </Card>

                <Card className="mb-6">
                  <CardContent className="pt-6">
                    <h3 className="text-xl font-semibold mb-3">Analyzing Content</h3>
                    <pre className="bg-muted p-4 rounded-md overflow-x-auto">
                      <code>
{`// Analyze the current page
window.browzyAI.analyzePage().then(result => {
  console.log(result);
});

// Analyze specific content
window.browzyAI.analyzeContent({
  text: 'Content to analyze',
  url: 'https://example.com',
  options: {
    depth: 'detailed',
    focus: 'sentiment'
  }
}).then(result => {
  console.log(result);
});`}
                      </code>
                    </pre>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="pt-6">
                    <h3 className="text-xl font-semibold mb-3">Event Listeners</h3>
                    <pre className="bg-muted p-4 rounded-md overflow-x-auto">
                      <code>
{`// Listen for analysis completion
window.browzyAI.on('analysisComplete', result => {
  console.log('Analysis completed:', result);
});

// Listen for errors
window.browzyAI.on('error', error => {
  console.error('Browzy AI error:', error);
});`}
                      </code>
                    </pre>
                  </CardContent>
                </Card>
              </motion.section>
            </motion.div>
          </TabsContent>

          <TabsContent value="features">
            <motion.div
              variants={container}
              initial="hidden"
              animate="show"
              className="prose dark:prose-invert prose-lg max-w-none font-inter"
            >
              <motion.section variants={item} className="mb-10">
                <h2 className="text-2xl font-bold mb-4">Core Features</h2>

                <Card className="mb-6">
                  <CardContent className="pt-6 flex gap-4">
                    <div className="flex-shrink-0">
                      <Search className="w-10 h-10 text-primary" />
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold mb-2">Smart Search</h3>
                      <p className="text-muted-foreground">
                        Enhance your browsing with AI-powered search capabilities. Simply type your query in natural language, and Browzy AI will understand your intent and provide relevant results.
                      </p>
                    </div>
                  </CardContent>
                </Card>

                <Card className="mb-6">
                  <CardContent className="pt-6 flex gap-4">
                    <div className="flex-shrink-0">
                      <MessageSquare className="w-10 h-10 text-primary" />
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold mb-2">Content Analysis</h3>
                      <p className="text-muted-foreground">
                        Get instant insights about any webpage you're browsing. Browzy AI can summarize content, detect bias, fact-check claims, and provide context for complex topics.
                      </p>
                    </div>
                  </CardContent>
                </Card>

                <Card className="mb-6">
                  <CardContent className="pt-6 flex gap-4">
                    <div className="flex-shrink-0">
                      <Zap className="w-10 h-10 text-primary" />
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold mb-2">Productivity Tools</h3>
                      <p className="text-muted-foreground">
                        Boost your productivity with AI-powered tools like text summarization, email drafting, code explanation, and more. Browzy AI integrates seamlessly with your workflow.
                      </p>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="pt-6 flex gap-4">
                    <div className="flex-shrink-0">
                      <FileText className="w-10 h-10 text-primary" />
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold mb-2">Chat History</h3>
                      <p className="text-muted-foreground">
                        Keep track of your interactions with Browzy AI. Your chat history is saved locally and can be exported or cleared at any time.
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </motion.section>
            </motion.div>
          </TabsContent>

          <TabsContent value="faq">
            <motion.div
              variants={container}
              initial="hidden"
              animate="show"
              className="prose dark:prose-invert prose-lg max-w-none font-inter"
            >
              <motion.section variants={item} className="mb-10">
                <h2 className="text-2xl font-bold mb-4">Frequently Asked Questions</h2>

                <div className="space-y-6">
                  <div>
                    <h3 className="text-xl font-semibold mb-2">Is Browzy AI free to use?</h3>
                    <p className="text-muted-foreground">
                      Browzy AI offers a free tier with limited features and usage. For unlimited access and advanced features, we offer Pro and Ultimate subscription plans. Visit our <a href="/plans" className="text-primary hover:underline">Pricing page</a> for more details.
                    </p>
                  </div>

                  <div>
                    <h3 className="text-xl font-semibold mb-2">How does Browzy AI protect my privacy?</h3>
                    <p className="text-muted-foreground">
                      We take privacy seriously. Browzy AI only processes the content you explicitly ask it to analyze. Your data is not stored permanently on our servers, and we do not sell your information to third parties. For more details, please read our <a href="/privacy-policy" className="text-primary hover:underline">Privacy Policy</a>.
                    </p>
                  </div>

                  <div>
                    <h3 className="text-xl font-semibold mb-2">Which browsers are supported?</h3>
                    <p className="text-muted-foreground">
                      Browzy AI currently supports Chrome and other Chromium-based browsers (Edge, Brave, Opera). Support for Firefox and Safari is coming soon.
                    </p>
                  </div>

                  <div>
                    <h3 className="text-xl font-semibold mb-2">Can I use Browzy AI offline?</h3>
                    <p className="text-muted-foreground">
                      Browzy AI requires an internet connection to function as it relies on cloud-based AI models for processing. However, some basic features may work offline with limited functionality.
                    </p>
                  </div>

                  <div>
                    <h3 className="text-xl font-semibold mb-2">How do I cancel my subscription?</h3>
                    <p className="text-muted-foreground">
                      You can cancel your subscription at any time by contacting our support team. Your access will continue until the end of your current billing period.
                    </p>
                  </div>

                  <div>
                    <h3 className="text-xl font-semibold mb-2">I found a bug. How do I report it?</h3>
                    <p className="text-muted-foreground">
                      We appreciate bug reports! Please contact us through our <a href="/contact" className="text-primary hover:underline">Contact page</a> with details about the issue, including steps to reproduce it, your browser version, and any error messages you received.
                    </p>
                  </div>
                </div>
              </motion.section>
            </motion.div>
          </TabsContent>
        </Tabs>
      </div>

      <Footer />
    </div>
  );
}
