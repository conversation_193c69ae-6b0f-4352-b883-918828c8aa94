import { useEffect, useState, useRef } from 'react';

interface WaveFieldProps {
  primaryColor?: string;
  secondaryColor?: string;
  particleCount?: number;
  particleSize?: number;
  waveSpeed?: number;
  interactionRadius?: number;
  interactionStrength?: number;
}

interface Particle {
  id: number;
  x: number;
  y: number;
  baseX: number;
  baseY: number;
  size: number;
  color: string;
  speed: number;
  amplitude: number;
  phase: number;
}

export function WaveField({
  primaryColor = 'rgba(0, 166, 192, 0.7)',
  secondaryColor = 'rgba(72, 215, 206, 0.5)',
  particleCount = 120,
  particleSize = 3,
  waveSpeed = 0.5,
  interactionRadius = 150,
  interactionStrength = 0.8
}: WaveFieldProps) {
  const [particles, setParticles] = useState<Particle[]>([]);
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });
  const [mousePos, setMousePos] = useState({ x: 0, y: 0 });
  const [isMouseInContainer, setIsMouseInContainer] = useState(false);
  
  const containerRef = useRef<HTMLDivElement>(null);
  const animationFrameRef = useRef<number>(0);
  const timeRef = useRef<number>(0);

  // Initialize particles
  useEffect(() => {
    if (!containerRef.current) return;
    
    const updateDimensions = () => {
      if (!containerRef.current) return;
      
      const rect = containerRef.current.getBoundingClientRect();
      setDimensions({
        width: rect.width,
        height: rect.height
      });
    };
    
    updateDimensions();
    
    // Create initial particles in a grid pattern
    const createParticles = () => {
      if (!containerRef.current) return;
      
      const { width, height } = dimensions;
      if (width === 0 || height === 0) return;
      
      // Calculate grid dimensions
      const cols = Math.ceil(Math.sqrt(particleCount * width / height));
      const rows = Math.ceil(particleCount / cols);
      
      const cellWidth = width / cols;
      const cellHeight = height / rows;
      
      const newParticles: Particle[] = [];
      
      for (let i = 0; i < particleCount; i++) {
        const col = i % cols;
        const row = Math.floor(i / cols);
        
        // Base position (center of cell with some randomness)
        const baseX = (col + 0.5) * cellWidth + (Math.random() - 0.5) * cellWidth * 0.5;
        const baseY = (row + 0.5) * cellHeight + (Math.random() - 0.5) * cellHeight * 0.5;
        
        // Random parameters for wave motion
        const speed = Math.random() * 0.5 + 0.5; // 0.5 to 1
        const amplitude = Math.random() * 15 + 5; // 5 to 20
        const phase = Math.random() * Math.PI * 2; // 0 to 2π
        
        // Alternate colors
        const useSecondary = Math.random() < 0.3;
        const color = useSecondary ? secondaryColor : primaryColor;
        
        // Random size variation
        const size = Math.random() * particleSize * 0.5 + particleSize * 0.75;
        
        newParticles.push({
          id: i,
          x: baseX,
          y: baseY,
          baseX,
          baseY,
          size,
          color,
          speed,
          amplitude,
          phase
        });
      }
      
      setParticles(newParticles);
    };
    
    createParticles();
    
    const handleResize = () => {
      updateDimensions();
      createParticles();
    };
    
    window.addEventListener('resize', handleResize);
    
    return () => {
      window.removeEventListener('resize', handleResize);
      cancelAnimationFrame(animationFrameRef.current);
    };
  }, [dimensions.width, dimensions.height, particleCount, particleSize, primaryColor, secondaryColor]);

  // Handle mouse interaction
  useEffect(() => {
    if (!containerRef.current) return;
    
    const handleMouseMove = (e: MouseEvent) => {
      if (!containerRef.current) return;
      
      const rect = containerRef.current.getBoundingClientRect();
      setMousePos({
        x: e.clientX - rect.left,
        y: e.clientY - rect.top
      });
    };
    
    const handleMouseEnter = () => {
      setIsMouseInContainer(true);
    };
    
    const handleMouseLeave = () => {
      setIsMouseInContainer(false);
    };
    
    containerRef.current.addEventListener('mousemove', handleMouseMove);
    containerRef.current.addEventListener('mouseenter', handleMouseEnter);
    containerRef.current.addEventListener('mouseleave', handleMouseLeave);
    
    return () => {
      if (containerRef.current) {
        containerRef.current.removeEventListener('mousemove', handleMouseMove);
        containerRef.current.removeEventListener('mouseenter', handleMouseEnter);
        containerRef.current.removeEventListener('mouseleave', handleMouseLeave);
      }
    };
  }, []);

  // Animate particles
  useEffect(() => {
    if (!containerRef.current || particles.length === 0) return;
    
    const animate = (timestamp: number) => {
      if (!timeRef.current) {
        timeRef.current = timestamp;
      }
      
      const elapsed = timestamp - timeRef.current;
      timeRef.current = timestamp;
      
      setParticles(prev => 
        prev.map(particle => {
          // Base wave motion
          const time = timestamp * 0.001 * waveSpeed * particle.speed;
          const waveX = Math.sin(time + particle.phase) * particle.amplitude;
          const waveY = Math.cos(time * 0.7 + particle.phase) * particle.amplitude * 0.5;
          
          // Mouse interaction
          let mouseX = 0;
          let mouseY = 0;
          
          if (isMouseInContainer) {
            const dx = mousePos.x - particle.baseX;
            const dy = mousePos.y - particle.baseY;
            const distance = Math.sqrt(dx * dx + dy * dy);
            
            if (distance < interactionRadius) {
              // Calculate repulsion strength (stronger when closer)
              const strength = (1 - distance / interactionRadius) * interactionStrength;
              
              // Repel from mouse
              const angle = Math.atan2(dy, dx);
              mouseX = -Math.cos(angle) * strength * 30;
              mouseY = -Math.sin(angle) * strength * 30;
            }
          }
          
          return {
            ...particle,
            x: particle.baseX + waveX + mouseX,
            y: particle.baseY + waveY + mouseY
          };
        })
      );
      
      animationFrameRef.current = requestAnimationFrame(animate);
    };
    
    animationFrameRef.current = requestAnimationFrame(animate);
    
    return () => {
      cancelAnimationFrame(animationFrameRef.current);
    };
  }, [particles, mousePos, isMouseInContainer, waveSpeed, interactionRadius, interactionStrength]);

  return (
    <div
      ref={containerRef}
      className="absolute inset-0 pointer-events-auto z-10 overflow-hidden"
    >
      {particles.map(particle => {
        // Calculate distance from mouse for glow effect
        let glowIntensity = 1;
        
        if (isMouseInContainer) {
          const dx = mousePos.x - particle.x;
          const dy = mousePos.y - particle.y;
          const distance = Math.sqrt(dx * dx + dy * dy);
          
          if (distance < interactionRadius * 1.5) {
            // Stronger glow when closer to mouse
            glowIntensity = 1 + (1 - Math.min(1, distance / interactionRadius)) * 2;
          }
        }
        
        // Determine shadow size based on particle size and glow intensity
        const shadowSize = particle.size * 3 * glowIntensity;
        
        return (
          <div
            key={particle.id}
            className="absolute rounded-full"
            style={{
              left: `${particle.x}px`,
              top: `${particle.y}px`,
              width: `${particle.size}px`,
              height: `${particle.size}px`,
              backgroundColor: particle.color,
              boxShadow: `0 0 ${shadowSize}px ${shadowSize / 2}px ${particle.color}`,
              opacity: 0.8,
              transition: 'box-shadow 0.3s ease-out',
            }}
          />
        );
      })}
    </div>
  );
}
