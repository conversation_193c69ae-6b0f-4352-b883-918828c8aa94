export const FEATURES = [
  {
    id: 1,
    icon: "Glasses",
    title: "Content Analysis",
    description: "Instantly analyzes any web page and provides insights with a sarcastic twist. Or as we call it, \"digital judging.\"",
    commandText: "learn_more --if_you_can",
    color: "primary"
  },
  {
    id: 2,
    icon: "Bot",
    title: "Sarcastic AI",
    description: "Our AI doesn't just analyze pages, it judges them (and you) mercilessly. Finally, an AI that's as pretentious as your barista.",
    commandText: "witness_sass --level=extreme",
    color: "secondary"
  },
  {
    id: 3,
    icon: "LineChart",
    title: "Smart Suggestions",
    description: "Get actually useful recommendations while browsing. We'll help you find gems online (unlike your ex's Spotify playlists).",
    commandText: "get_smarter --or_die_trying",
    color: "accent"
  },
  {
    id: 4,
    icon: "Flame",
    title: "Roast Mode",
    description: "Enable this when you're feeling masochistic. We'll critique your browsing habits with the ferocity of your disappointed parents.",
    commandText: "activate_pain --emotional",
    color: "secondary"
  },
  {
    id: 5,
    icon: "ShieldCheck",
    title: "BS Detector",
    description: "Automatically flags misleading content, fake news, and corporate propaganda. Even detects when influencers are faking their personalities.",
    commandText: "detect_lies --like_your_ex",
    color: "primary"
  },
  {
    id: 6,
    icon: "Zap",
    title: "Blazing Speed",
    description: "Unlike your dating app matches, browzy responds instantly. We process pages faster than you abandon your New Year's resolutions.",
    commandText: "speed_test --compared_to=your_ambition",
    color: "accent"
  }
];

export const TESTIMONIALS = [
  {
    id: 1,
    name: "Sarah K.",
    title: "Professional Procrastinator",
    text: "browzy told me my social media addiction was \"sadder than my Spotify wrapped.\" It hurt, but it wasn't wrong. Now I'm only wasting 4 hours daily instead of 6.",
    rating: 4.5
  },
  {
    id: 2,
    name: "Mike T.",
    title: "Tech Blogger",
    text: "This extension analyzed an article I wrote and called it \"as insightful as a fortune cookie written by a toddler.\" I'm simultaneously offended and impressed.",
    rating: 4
  },
  {
    id: 3,
    name: "Alex R.",
    title: "E-commerce Addict",
    text: "browzy saved me from buying yet another useless gadget by telling me \"your apartment has less free space than your dating calendar.\" Harsh but effective.",
    rating: 5
  }
];

export const BLOG_POSTS = [
  {
    id: 1,
    title: "Why Your Social Media Feed Is Like Fast Food: Addictive and Nutritionally Void",
    excerpt: "An AI's scathing analysis of how social media algorithms are designed to keep you scrolling through content as nutritious as styrofoam...",
    date: "Oct 21, 2023",
    tag: "HOT TAKE",
    tagColor: "secondary",
    image: "https://images.unsplash.com/photo-1563986768609-322da13575f3?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
    slug: "social-media-fast-food",
    commandText: "read_full_roast --with_popcorn"
  },
  {
    id: 2,
    title: "E-commerce Dark Patterns: How Websites Trick You Into Buying Crap You Don't Need",
    excerpt: "I analyzed 50 top e-commerce sites and found 37 different ways they manipulate you into purchases. Your wallet will thank me later...",
    date: "Oct 15, 2023",
    tag: "ANALYSIS",
    tagColor: "accent",
    image: "https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
    slug: "ecommerce-dark-patterns",
    commandText: "save_your_money --reveal_tricks"
  },
  {
    id: 3,
    title: "News Sites Are Just Clickbait Farms With Better Grammar (Barely)",
    excerpt: "I analyzed headlines from major news outlets and found that 78% were designed to trigger emotion rather than inform. Journalism is dead, and we killed it...",
    date: "Oct 5, 2023",
    tag: "EXPOSÉ",
    tagColor: "primary",
    image: "https://images.unsplash.com/photo-1504711434969-e33886168f5c?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
    slug: "news-clickbait-farms",
    commandText: "truth_bomb --with_receipts"
  }
];

export const EXAMPLE_TABS = [
  {
    id: "youtube",
    label: "YouTube",
    content: {
      url: "youtube.com/watch?v=dQw4w9WgXcQ",
      title: "Top 10 Productivity Hacks That Never Actually Work",
      channel: "ProductivityGuru",
      subscribers: "1.2M subscribers",
      thumbnail: "https://images.unsplash.com/photo-1611162617213-7d7a39e9b1d7?ixlib=rb-1.2.1&auto=format&fit=crop&w=1200&q=80",
      analysis: [
        { text: "Video Length: 15:03 (14:00 too long)", type: "normal" },
        { text: "Content Value: 2/10 (just like your dating profile)", type: "normal" },
        { text: "Clickbait Level: Extreme (shocking absolutely no one)", type: "normal" },
        { text: "Let me guess, you think watching this will make you productive instead of, you know, actually doing work?", type: "secondary" },
        { text: "Time Saved: 15 minutes. You're welcome.", type: "accent" }
      ],
      tldr: "This video can be summarized as \"make lists, wake up early, and other obvious advice that you'll screenshot and never look at again.\""
    }
  },
  {
    id: "news",
    label: "News",
    content: {
      url: "news-site.com/breaking-news",
      title: "You Won't BELIEVE What This Celebrity Said About Politics",
      source: "ClickNews",
      badge: "BREAKING",
      thumbnail: "https://images.unsplash.com/photo-1495020689067-958852a7765e?ixlib=rb-1.2.1&auto=format&fit=crop&w=1200&q=80",
      analysis: [
        { text: "Clickbait Score: 9.8/10 (impressively desperate)", type: "normal" },
        { text: "Actual News Value: -3/10 (yes, negative points)", type: "normal" },
        { text: "Reading this is actively making you less informed about the world.", type: "secondary" },
        { text: "Alternative suggestion: Read literally anything else.", type: "accent" }
      ],
      tldr: "A D-list celebrity had a lukewarm take that the site is pretending is controversial to get your outrage-clicks."
    }
  },
  {
    id: "shopping",
    label: "Shopping",
    content: {
      url: "amazon.com/overpriced-gadget",
      title: "UltraMax Pro 5000 Smart Toaster with WiFi & AI",
      price: "$299.99",
      rating: "4.2★",
      thumbnail: "https://images.unsplash.com/photo-1517142089942-ba376ce32a2e?ixlib=rb-1.2.1&auto=format&fit=crop&w=1200&q=80",
      analysis: [
        { text: "Unnecessary Technology Level: 11/10", type: "normal" },
        { text: "Features You'll Actually Use: 1/27", type: "normal" },
        { text: "Your current toaster works fine. This one will just judge your breakfast choices via an app.", type: "secondary" },
        { text: "Money saved by not buying: $299.99. You're welcome.", type: "accent" }
      ],
      tldr: "It's a toaster that costs as much as a smartphone but will be obsolete in 6 months when they release the UltraMax Pro 5001."
    }
  },
  {
    id: "social",
    label: "Social Media",
    content: {
      url: "instagram.com/influencer",
      title: "Living My Best Life #blessed #authentic #sponsored",
      poster: "LifestyleInfluencer",
      followers: "3.7M followers",
      thumbnail: "https://images.unsplash.com/photo-1565063289283-8609714e46af?ixlib=rb-1.2.1&auto=format&fit=crop&w=1200&q=80",
      analysis: [
        { text: "Authenticity: 0.3/10 (impressive commitment to fakeness)", type: "normal" },
        { text: "Filter Usage: 17 layers (practically CGI at this point)", type: "normal" },
        { text: "This photo took 143 attempts and the 'casual brunch' was cold by the time they ate it.", type: "secondary" },
        { text: "Reality check: Their life is as carefully curated as a museum exhibit.", type: "accent" }
      ],
      tldr: "A heavily staged photo to sell you products while pretending it's just their everyday life. #ad would be more honest."
    }
  }
];
