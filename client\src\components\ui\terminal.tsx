import { ReactNode } from 'react';
import clsx from 'clsx';

interface TerminalTextProps {
  children: ReactNode;
  type?: 'normal' | 'primary' | 'secondary' | 'accent';
  className?: string;
}

export function TerminalText({
  children,
  type = 'normal',
  className
}: TerminalTextProps) {
  const textColorClass =
    type === 'primary' ? 'text-primary' :
    type === 'secondary' ? 'text-secondary' :
    type === 'accent' ? 'text-accent' :
    'text-muted-foreground';

  return (
    <div className={clsx("terminal font-inter text-sm mb-2", textColorClass, className)}>
      {children}
    </div>
  );
}

export function TerminalContainer({
  children,
  className
}: {
  children: ReactNode;
  className?: string;
}) {
  return (
    <div className={clsx("space-y-3 font-inter text-base", className)}>
      {children}
    </div>
  );
}

export function TerminalTLDR({
  title = "TLDR:",
  children,
  className
}: {
  title?: string;
  children: ReactNode;
  className?: string;
}) {
  return (
    <div className={clsx("mt-6 p-4 bg-card rounded-lg border border-primary/50 text-sm shadow-md", className)}>
      <div className="text-primary font-medium mb-2 font-inter">{title}</div>
      <p className="font-inter">{children}</p>
    </div>
  );
}
