/**
 * Generates a random token of specified length
 * @param length Length of the token to generate
 * @returns A random alphanumeric token
 */
export function generateToken(length: number = 9): string {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  
  // Create a Uint8Array with the required number of random values
  const randomValues = new Uint8Array(length);
  
  // Fill the array with cryptographically secure random values if available
  if (window.crypto && window.crypto.getRandomValues) {
    window.crypto.getRandomValues(randomValues);
  } else {
    // Fallback to Math.random() if crypto API is not available
    for (let i = 0; i < length; i++) {
      randomValues[i] = Math.floor(Math.random() * 256);
    }
  }
  
  // Convert random values to characters
  for (let i = 0; i < length; i++) {
    result += characters.charAt(randomValues[i] % characters.length);
  }
  
  return result;
}

/**
 * Validates if a token has the correct format
 * @param token Token to validate
 * @param length Expected length of the token
 * @returns Boolean indicating if the token is valid
 */
export function validateToken(token: string, length: number = 9): boolean {
  if (!token || token.length !== length) {
    return false;
  }
  
  // Check if token contains only alphanumeric characters
  const alphanumericRegex = /^[a-zA-Z0-9]+$/;
  return alphanumericRegex.test(token);
}
