# Build stage
FROM node:20-alpine AS builder

# Set working directory
WORKDIR /app

# Install build dependencies
RUN apk add --no-cache python3 make g++

# Copy configuration files first
COPY package.json .
COPY tsconfig.json .
COPY tsconfig.node.json .
COPY vite.config.ts .
COPY postcss.config.js .
COPY tailwind.config.ts .
COPY index.html .

# Install all dependencies
RUN npm install

# Copy source code and public folders
COPY src ./src
COPY public ./public

# Build the application
RUN npm run build

# Development stage
FROM node:20-alpine

WORKDIR /app

# Install build dependencies
RUN apk add --no-cache python3 make g++

# Copy package files
COPY package.json .
COPY tsconfig.json .
COPY tsconfig.node.json .
COPY vite.config.ts .
COPY postcss.config.js .
COPY tailwind.config.ts .
COPY index.html .

# Install all dependencies
RUN npm install

# Copy source code and public folders
COPY src ./src
COPY public ./public

# Expose the default Vite port
EXPOSE 5173

# Start the development server
CMD ["npm", "run", "dev", "--", "--host"]