# Font Files for browzy

This directory is intended to store the font files for the browzy application.

## Required Fonts

The application uses the following premium fonts:

1. **<PERSON>yrene** by <PERSON><PERSON> (for headlines and subheadings)
   - A geometric sans serif with unique, squarish letterforms
   - Used for all headings and subheadings in the application

2. **Tiempos** by Klim Type Foundry (for body text)
   - A robust serif font for body text
   - Used for all body text and paragraphs

## How to Add the Fonts

Since these are premium fonts, you'll need to purchase licenses for them and add the font files to this directory. Here's how:

### Option 1: Self-hosting (Recommended)

1. Purchase the fonts from their respective foundries:
   - Styrene: Available from [Commercial Type](https://commercialtype.com/catalog/styrene)
   - Tiempos: Available from [Klim Type Foundry](https://klim.co.nz/retail-fonts/tiempos-text/)

2. Convert the fonts to web formats (WOFF2 and WOFF) using a tool like [Font Squirrel's Webfont Generator](https://www.fontsquirrel.com/tools/webfont-generator) or [Transfonter](https://transfonter.org/).

3. Place the font files in this directory with the following naming convention:
   - `StyreneA-Regular.woff2` and `StyreneA-Regular.woff`
   - `StyreneA-Medium.woff2` and `StyreneA-Medium.woff`
   - `StyreneA-Bold.woff2` and `StyreneA-Bold.woff`
   - `TiemposText-Regular.woff2` and `TiemposText-Regular.woff`
   - `TiemposText-RegularItalic.woff2` and `TiemposText-RegularItalic.woff`
   - `TiemposText-Medium.woff2` and `TiemposText-Medium.woff`
   - `TiemposText-MediumItalic.woff2` and `TiemposText-MediumItalic.woff`
   - `TiemposText-Semibold.woff2` and `TiemposText-Semibold.woff`
   - `TiemposText-SemiboldItalic.woff2` and `TiemposText-SemiboldItalic.woff`

4. Uncomment the font CSS link in `client/index.html`:
   ```html
   <link rel="stylesheet" href="/fonts/fonts.css">
   ```

### Option 2: Using Adobe Fonts

If you have an Adobe Creative Cloud subscription, you might be able to access these fonts through Adobe Fonts:

1. Add the fonts to your Adobe Fonts project
2. Follow Adobe's instructions to include the fonts in your web project
3. Update the font-family names in `tailwind.config.ts` to match the names provided by Adobe Fonts

### Option 3: Using a CDN

If the fonts are available through a CDN service:

1. Subscribe to the CDN service that provides these fonts
2. Include the CDN link in your HTML
3. Update the font-family names in `tailwind.config.ts` if necessary

## Fallback Fonts

Until you add the premium fonts, the application will use the following fallbacks:

- For Styrene: Helvetica Neue, Arial, sans-serif
- For Tiempos: Georgia, Cambria, Times New Roman, Times, serif

These fallbacks provide a similar aesthetic to the premium fonts but won't match exactly.
