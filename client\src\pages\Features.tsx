import { useEffect } from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import IntegrationSection from '@/components/IntegrationSection';
import { motion } from 'framer-motion';
import { FEATURES } from '@/lib/constants';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  LineChart,
  Flame,
  ShieldCheck,
  Zap
} from 'lucide-react';

const iconComponents = {
  <PERSON><PERSON>,
  Glasses,
  LineChart,
  Flame,
  ShieldCheck,
  Zap
};

export default function Features() {
  useEffect(() => {
    // Update document title
    document.title = "Features - browzy - Supercharge Your Browser with AI";

    // Add meta description
    const metaDescription = document.createElement('meta');
    metaDescription.name = 'description';
    metaDescription.content = 'Explore the powerful features of browzy - Content Analysis, AI Insights, Smart Suggestions, and more. Supercharge your browsing experience.';
    document.head.appendChild(metaDescription);

    window.scrollTo(0, 0);

    return () => {
      document.head.removeChild(metaDescription);
    };
  }, []);

  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0, transition: { duration: 0.5 } }
  };

  const getIconComponent = (iconName: string) => {
    const IconComponent = iconComponents[iconName as keyof typeof iconComponents];
    return IconComponent ? <IconComponent className="w-6 h-6" /> : null;
  };

  const getColorClass = (color: string) => {
    switch (color) {
      case 'primary': return 'text-primary';
      case 'secondary': return 'text-secondary';
      case 'accent': return 'text-secondary';
      default: return 'text-primary';
    }
  };

  const getGradientClass = (color: string) => {
    switch (color) {
      case 'primary': return 'from-primary/20 to-transparent';
      case 'secondary': return 'from-secondary/20 to-transparent';
      case 'accent': return 'from-secondary/20 to-primary/20';
      default: return 'from-primary/20 to-transparent';
    }
  };

  return (
    <div className="min-h-screen dark:bg-black light:bg-white text-foreground overflow-hidden">
      <Header />

      <div className="max-w-6xl mx-auto px-6 sm:px-8 lg:px-12 pt-28 md:pt-32">
        <main className="py-8 md:py-12">
          <section className="py-16 md:py-24 relative">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="text-center mb-20"
            >
              <h2 className="font-inter text-5xl md:text-6xl font-bold tracking-tight">
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary">Features</span> That Actually Work
              </h2>
              <p className="font-inter text-lg md:text-xl text-muted-foreground mt-6 max-w-2xl mx-auto leading-relaxed">
                Unlike your dating profile, these features actually deliver what they promise.
                Explore the full capabilities of browzy that will transform your browsing experience.
              </p>
            </motion.div>

            <motion.div
              className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 md:gap-10"
              variants={container}
              initial="hidden"
              animate="show"
              viewport={{ once: true, amount: 0.2 }}
            >
              {FEATURES.map((feature) => (
                <motion.div key={feature.id} variants={item}>
                  <motion.div 
                    className="bg-card rounded-xl overflow-hidden border border-muted shadow-xl relative group hover:border-primary/30 transition-all duration-500 h-full flex flex-col p-6 md:p-8"
                    whileHover={{
                      boxShadow: "0 10px 30px rgba(0, 166, 192, 0.2), 0 0 0 1px rgba(0, 166, 192, 0.1)"
                    }}
                  >
                    <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-secondary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none"></div>

                    <div className="flex items-center mb-6 relative z-10">
                      <div className={`w-16 h-16 rounded-xl flex items-center justify-center shadow-lg bg-gradient-to-br ${getGradientClass(feature.color)}`}>
                        <div className={`${getColorClass(feature.color)} w-8 h-8`}>
                          {getIconComponent(feature.icon)}
                        </div>
                      </div>
                      <div className="ml-4">
                        <h3 className="font-inter text-2xl font-semibold group-hover:text-primary transition-colors duration-300 tracking-tight">
                          {feature.title}
                        </h3>
                      </div>
                    </div>

                    <p className="text-muted-foreground flex-grow mb-6 font-inter text-lg leading-relaxed relative z-10">
                      {feature.description}
                    </p>

                    <div className="bg-background/50 backdrop-blur-sm py-3 px-4 rounded-lg text-primary font-mono text-sm inline-flex items-center border border-muted/30 group-hover:border-primary/20 transition-all duration-300 relative z-10">
                      <span className="mr-2 opacity-70">&gt;</span>
                      {feature.commandText}
                    </div>
                  </motion.div>
                </motion.div>
              ))}
            </motion.div>

            <div className="mt-24 mb-16 relative">
              <div className="h-px w-full bg-gradient-to-r from-transparent via-primary/40 to-transparent"></div>
              <div className="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 w-20 h-20">
                <div className="absolute inset-0 bg-gradient-to-r from-primary/30 to-accent/30 rounded-full blur-xl animate-pulse-slow"></div>
                <div className="absolute inset-3 bg-gradient-to-r from-primary/20 to-accent/20 rounded-full blur-md animate-pulse-slow-reverse"></div>
              </div>
            </div>

            <motion.div
              className="text-center"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <h2 className="font-inter text-3xl md:text-4xl font-bold mb-8">
                Ready to Experience These Features?
              </h2>

              <div className="relative group inline-block">
                <div className="absolute -inset-0.5 bg-gradient-to-r from-primary to-secondary rounded-xl blur opacity-70 group-hover:opacity-100 transition duration-500 group-hover:duration-200 animate-pulse-slow"></div>
                <a
                  href="#download"
                  className="relative inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-primary to-secondary text-white rounded-xl hover:shadow-xl transition-all duration-300 transform group-hover:scale-105 font-medium shadow-md text-lg font-inter"
                >
                  Install browzy Now
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="ml-3 group-hover:translate-x-1 transition-transform duration-300">
                    <line x1="5" y1="12" x2="19" y2="12"></line>
                    <polyline points="12 5 19 12 12 19"></polyline>
                  </svg>
                </a>
              </div>

              <p className="text-muted-foreground mt-6 font-inter">
                Free to use. No credit card required.
              </p>
            </motion.div>
          </section>

          <IntegrationSection />
        </main>
        <Footer />
      </div>
    </div>
  );
}
