import { useEffect } from 'react';
import Header from '@/components/Header';
import HeroSection from '@/components/HeroSection';
import ExamplesSection from '@/components/ExamplesSection';
import IntegrationSection from '@/components/IntegrationSection';
import BlogSection from '@/components/BlogSection';
import ContactSection from '@/components/ContactSection';
import DownloadCTA from '@/components/DownloadCTA';
import Footer from '@/components/Footer';

export default function Home() {
  useEffect(() => {
    // Update document title
    document.title = "browzy - Supercharge Your Browser with AI";

    // Add Font Awesome script
    const script = document.createElement('script');
    script.src = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/js/all.min.js';
    script.async = true;
    document.body.appendChild(script);

    // Add meta description
    const metaDescription = document.createElement('meta');
    metaDescription.name = 'description';
    metaDescription.content = 'browzy - The AI Chrome extension that supercharges your browser with intelligent insights and analysis. Enhance your browsing experience with browzy.';
    document.head.appendChild(metaDescription);

    return () => {
      document.body.removeChild(script);
      document.head.removeChild(metaDescription);
    };
  }, []);

  return (
    <div className="min-h-screen dark:bg-black light:bg-white text-foreground overflow-hidden">
      <Header />
      <HeroSection />

      <div className="max-w-6xl mx-auto px-6 sm:px-8 lg:px-12">
        <main className="py-8 md:py-12">
          <ExamplesSection />
          <BlogSection />
          <ContactSection />
          <DownloadCTA />
        </main>
        <Footer />
      </div>
    </div>
  );
}
