import { useState } from 'react';
import { motion } from 'framer-motion';
import { EXAMPLE_TABS } from '@/lib/constants';
import { CyberpunkSeparator } from './ui/separator';
import { TerminalContainer, TerminalText, TerminalTLDR } from './ui/terminal';
import { Tabs, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';

export default function ExamplesSection() {
  const [activeTab, setActiveTab] = useState(EXAMPLE_TABS[0].id);

  return (
    <section id="examples" className="py-16 relative">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="text-center mb-20"
        >
          <h2 className="font-inter text-5xl md:text-6xl font-bold tracking-tight">
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary">See It In Action</span>
          </h2>
          <p className="font-inter text-lg md:text-xl text-muted-foreground mt-6 max-w-2xl mx-auto leading-relaxed">
            Here's how browzy handles different websites. Spoiler alert: with brutal honesty and questionable tact.
          </p>
        </motion.div>

        <Tabs defaultValue={EXAMPLE_TABS[0].id} className="w-full" onValueChange={setActiveTab}>
          <div className="flex justify-center mb-12 overflow-x-auto">
            <TabsList className="inline-flex bg-card rounded-xl p-1.5 shadow-md border border-muted/50">
              {EXAMPLE_TABS.map((tab) => (
                <TabsTrigger
                  key={tab.id}
                  value={tab.id}
                  className={`px-6 py-3 rounded-lg font-inter text-base font-medium transition-all duration-300 ${
                    activeTab === tab.id
                      ? 'bg-background text-primary shadow-sm'
                      : 'text-muted-foreground hover:text-foreground hover:bg-background/50'
                  }`}
                >
                  {tab.label}
                </TabsTrigger>
              ))}
            </TabsList>
          </div>

          {EXAMPLE_TABS.map((tab) => (
            <TabsContent key={tab.id} value={tab.id} className="w-full">
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
                className="bg-card rounded-xl overflow-hidden border border-muted shadow-xl relative group hover:border-primary/30 transition-all duration-500"
                whileHover={{
                  boxShadow: "0 10px 30px rgba(0, 166, 192, 0.2), 0 0 0 1px rgba(0, 166, 192, 0.1)"
                }}
              >
                <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-secondary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none"></div>
                <div className="p-3 bg-background/90 backdrop-blur-sm flex items-center gap-2 border-b border-muted/50 relative z-10">
                  <div className="flex items-center gap-2">
                    <div className="w-3.5 h-3.5 rounded-full bg-secondary hover:bg-secondary/80 transition-colors cursor-pointer"></div>
                    <div className="w-3.5 h-3.5 rounded-full bg-accent hover:bg-accent/80 transition-colors cursor-pointer"></div>
                    <div className="w-3.5 h-3.5 rounded-full bg-primary hover:bg-primary/80 transition-colors cursor-pointer"></div>
                  </div>
                  <div className="ml-4 px-4 py-1.5 text-sm font-inter text-muted-foreground bg-muted/30 rounded-full flex-grow max-w-md mx-auto text-center border border-muted/20">
                    {tab.content.url}
                  </div>
                  <div className="flex items-center gap-2 text-muted-foreground">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="cursor-pointer hover:text-primary transition-colors">
                      <path d="M18 21a6 6 0 0 0-12 0"></path>
                      <circle cx="12" cy="9" r="6"></circle>
                    </svg>
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="cursor-pointer hover:text-primary transition-colors">
                      <path d="M12 3v18"></path>
                      <path d="M18 9H6"></path>
                    </svg>
                  </div>
                </div>

                <div className="grid md:grid-cols-3 gap-8 p-8 relative">
                  <div className="absolute top-0 right-0 w-1/3 h-1/3 bg-gradient-to-br from-primary/10 to-secondary/5 rounded-full blur-3xl opacity-30 pointer-events-none"></div>
                  <div className="absolute bottom-0 left-0 w-1/4 h-1/4 bg-gradient-to-tr from-accent/10 to-primary/5 rounded-full blur-3xl opacity-30 pointer-events-none"></div>
                  <div className="col-span-2">
                    <div className="aspect-video bg-background rounded-xl relative overflow-hidden shadow-lg group border border-muted/50 hover:border-primary/30 transition-all duration-500">
                      <div className="absolute inset-0 bg-gradient-to-br from-primary/10 to-secondary/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500 z-10 pointer-events-none"></div>
                      <img
                        src={tab.content.thumbnail}
                        alt="Content thumbnail"
                        className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-105"
                      />
                      <div className="absolute inset-0 flex items-center justify-center z-20">
                        <div className="w-24 h-24 bg-gradient-to-r from-primary to-secondary opacity-90 rounded-full flex items-center justify-center shadow-xl transform transition-all duration-500 group-hover:scale-110 group-hover:shadow-primary/30 relative">
                          <div className="absolute inset-1 rounded-full bg-gradient-to-r from-primary/80 to-secondary/80 blur-sm"></div>
                          <div className="absolute inset-0 rounded-full animate-pulse-slow opacity-70"></div>
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            className="w-10 h-10 text-white ml-1 relative z-10"
                          >
                            <polygon points="5 3 19 12 5 21 5 3"></polygon>
                          </svg>
                        </div>
                      </div>
                      <div className="absolute bottom-3 right-3 bg-background/80 backdrop-blur-sm px-3 py-1 rounded-full text-xs font-inter text-muted-foreground border border-muted/30 z-20">
                        3:45
                      </div>
                    </div>

                    <div className="mt-8 bg-background/50 backdrop-blur-sm p-5 rounded-xl border border-muted/30 shadow-sm hover:shadow-md transition-all duration-300 hover:border-primary/20">
                      <h3 className="font-inter text-xl font-semibold bg-clip-text text-transparent bg-gradient-to-r from-foreground to-foreground/90">{tab.content.title}</h3>
                      <div className="flex flex-wrap items-center mt-4 gap-2">
                        <div className="w-12 h-12 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-full mr-3 flex items-center justify-center shadow-sm p-0.5">
                          <div className="w-full h-full bg-background rounded-full flex items-center justify-center overflow-hidden">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-6 h-6 text-primary">
                              <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                              <circle cx="12" cy="7" r="4"></circle>
                            </svg>
                          </div>
                        </div>
                        <div className="flex flex-col">
                          <span className="text-foreground font-inter font-medium">
                            {tab.content.channel || tab.content.source || tab.content.poster}
                          </span>
                          <span className="text-muted-foreground text-sm font-inter">
                            Published 2 days ago
                          </span>
                        </div>
                        {(tab.content.subscribers || tab.content.badge || tab.content.followers || tab.content.rating || tab.content.price) && (
                          <span className="ml-auto text-sm bg-primary/10 text-primary rounded-full px-4 py-1.5 font-inter font-medium border border-primary/20">
                            {tab.content.subscribers || tab.content.badge || tab.content.followers || tab.content.rating || tab.content.price}
                          </span>
                        )}
                      </div>
                      <div className="flex flex-wrap gap-2 mt-4">
                        <span className="text-xs bg-muted/50 text-muted-foreground rounded-full px-3 py-1 font-inter">
                          #browsing
                        </span>
                        <span className="text-xs bg-muted/50 text-muted-foreground rounded-full px-3 py-1 font-inter">
                          #ai
                        </span>
                        <span className="text-xs bg-muted/50 text-muted-foreground rounded-full px-3 py-1 font-inter">
                          #browzy
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="bg-background p-6 rounded-xl shadow-lg border border-muted/50 hover:border-primary/30 transition-all duration-500 relative group">
                    <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-secondary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-xl pointer-events-none"></div>

                    <div className="flex items-center justify-between mb-6 relative z-10">
                      <div className="flex items-center">
                        <div className="w-8 h-8 bg-gradient-to-r from-primary to-secondary rounded-lg flex items-center justify-center shadow-md mr-3">
                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-white">
                            <path d="M20.24 12.24a6 6 0 0 0-8.49-8.49L5 10.5V19h8.5l6.74-6.76z"></path>
                            <line x1="16" y1="8" x2="2" y2="22"></line>
                            <line x1="17.5" y1="15" x2="9" y2="15"></line>
                          </svg>
                        </div>
                        <div className="font-inter text-xl font-semibold bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary">browzy Analysis</div>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-primary animate-pulse"></div>
                        <span className="text-sm bg-primary/20 text-primary rounded-full px-4 py-1 font-inter font-medium border border-primary/20">LIVE</span>
                      </div>
                    </div>

                    <div className="bg-card/50 backdrop-blur-sm p-5 rounded-xl border border-muted/30 relative z-10">
                      <TerminalContainer>
                        {tab.content.analysis.map((line, index) => (
                          <TerminalText
                            key={index}
                            type={line.type === 'normal' ? 'normal' : line.type === 'secondary' ? 'secondary' : 'accent'}
                          >
                            {line.text}
                          </TerminalText>
                        ))}

                        <TerminalTLDR>
                          {tab.content.tldr}
                        </TerminalTLDR>
                      </TerminalContainer>
                    </div>

                    <div className="flex justify-between items-center mt-5 text-xs text-muted-foreground font-inter relative z-10">
                      <div>Analysis completed in 0.8s</div>
                      <div className="flex items-center gap-3">
                        <button className="hover:text-primary transition-colors flex items-center gap-1">
                          <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <path d="M14 9V5a3 3 0 0 0-3-3l-4 9v11h11.28a2 2 0 0 0 2-1.7l1.38-9a2 2 0 0 0-2-2.3zM7 22H4a2 2 0 0 1-2-2v-7a2 2 0 0 1 2-2h3"></path>
                          </svg>
                          Helpful
                        </button>
                        <button className="hover:text-secondary transition-colors flex items-center gap-1">
                          <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <path d="M10 15v4a3 3 0 0 0 3 3l4-9V2H5.72a2 2 0 0 0-2 1.7l-1.38 9a2 2 0 0 0 2 2.3zm7-13h2.67A2.31 2.31 0 0 1 22 4v7a2.31 2.31 0 0 1-2.33 2H17"></path>
                          </svg>
                          Not helpful
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            </TabsContent>
          ))}
        </Tabs>

        <div className="mt-24 mb-16 relative">
          <div className="h-px w-full bg-gradient-to-r from-transparent via-primary/40 to-transparent"></div>
          <div className="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 w-20 h-20">
            <div className="absolute inset-0 bg-gradient-to-r from-primary/30 to-accent/30 rounded-full blur-xl animate-pulse-slow"></div>
            <div className="absolute inset-3 bg-gradient-to-r from-primary/20 to-accent/20 rounded-full blur-md animate-pulse-slow-reverse"></div>
          </div>
        </div>
    </section>
  );
}
