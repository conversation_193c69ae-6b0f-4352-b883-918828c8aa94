import { supabaseAdmin } from '../supabase';

/**
 * Interface for user connection data
 */
interface UserConnectionData {
  userId: string;
  tokenId?: string;
  extensionVersion?: string;
  browserName?: string;
  browserVersion?: string;
  osName?: string;
  osVersion?: string;
  deviceType?: string;
  ipAddress?: string;
  userAgent?: string;
}

/**
 * Interface for user metrics
 */
interface UserMetrics {
  userId: string;
  firstConnectionTime: string;
  lastConnectionTime: string;
  totalConnections: number;
  totalChatMessages: number;
  totalConnectionDuration: number;
  lastActiveTime: string;
  favoriteBrowser?: string;
  favoriteDeviceType?: string;
}

/**
 * Record a new user connection
 * @param data Connection data
 * @returns Success status
 */
export async function recordUserConnection(data: UserConnectionData): Promise<{ 
  success: boolean; 
  connectionId?: string;
  error?: any 
}> {
  try {
    console.log('Recording user connection:', data);
    
    // Insert connection record
    try {
      const { data: connectionData, error } = await supabaseAdmin
        .from('user_connections')
        .insert({
          user_id: data.userId,
          token_id: data.tokenId,
          extension_version: data.extensionVersion,
          browser_name: data.browserName,
          browser_version: data.browserVersion,
          os_name: data.osName,
          os_version: data.osVersion,
          device_type: data.deviceType,
          ip_address: data.ipAddress,
          user_agent: data.userAgent,
          connection_time: new Date().toISOString(),
          is_successful: true
        })
        .select('id')
        .single();

      if (error) {
        console.error('Database error when recording connection:', error);
        
        // If the error is that the table doesn't exist, return success anyway for development
        if (error.code === '42P01' && process.env.NODE_ENV !== 'production') { // PostgreSQL error code for undefined_table
          console.log('Table does not exist, but accepting connection in development mode');
          return { success: true };
        }
        
        throw error;
      }
      
      console.log('Connection recorded successfully:', connectionData?.id);
      return { success: true, connectionId: connectionData?.id };
    } catch (dbError) {
      console.error('Error recording connection to database:', dbError);
      
      // For development, return success even if database operation fails
      if (process.env.NODE_ENV !== 'production') {
        console.log('Development mode: Returning success despite database error');
        return { success: true };
      }
      
      throw dbError;
    }
  } catch (error) {
    console.error('Error recording user connection:', error);
    return { success: false, error };
  }
}

/**
 * Record user disconnection
 * @param userId User ID
 * @param connectionId Connection ID
 * @returns Success status
 */
export async function recordUserDisconnection(userId: string, connectionId?: string): Promise<{ 
  success: boolean; 
  error?: any 
}> {
  try {
    console.log('Recording user disconnection:', userId, connectionId);
    
    if (!connectionId) {
      // If no connection ID provided, find the latest connection for this user
      const { data: latestConnection, error: findError } = await supabaseAdmin
        .from('user_connections')
        .select('id, connection_time')
        .eq('user_id', userId)
        .is('disconnect_time', null)
        .order('connection_time', { ascending: false })
        .limit(1)
        .single();
      
      if (findError && findError.code !== 'PGRST116') { // PGRST116 is "no rows returned"
        console.error('Error finding latest connection:', findError);
        
        // For development, return success even if database operation fails
        if (process.env.NODE_ENV !== 'production') {
          return { success: true };
        }
        
        throw findError;
      }
      
      connectionId = latestConnection?.id;
    }
    
    if (!connectionId) {
      console.log('No active connection found for user:', userId);
      return { success: true };
    }
    
    // Calculate connection duration
    const now = new Date();
    let connectionDuration = 0;
    
    try {
      const { data: connection, error } = await supabaseAdmin
        .from('user_connections')
        .select('connection_time')
        .eq('id', connectionId)
        .single();
      
      if (!error && connection) {
        const connectionTime = new Date(connection.connection_time);
        connectionDuration = Math.floor((now.getTime() - connectionTime.getTime()) / 1000);
      }
    } catch (error) {
      console.error('Error calculating connection duration:', error);
    }
    
    // Update connection record
    try {
      const { error } = await supabaseAdmin
        .from('user_connections')
        .update({
          disconnect_time: now.toISOString(),
          connection_duration: connectionDuration
        })
        .eq('id', connectionId);
      
      if (error) {
        console.error('Error updating connection record:', error);
        
        // For development, return success even if database operation fails
        if (process.env.NODE_ENV !== 'production') {
          return { success: true };
        }
        
        throw error;
      }
      
      // Update user metrics
      await supabaseAdmin
        .from('user_metrics')
        .update({
          total_connection_duration: supabaseAdmin.rpc('increment_field', { 
            row_id: userId, 
            field_name: 'total_connection_duration', 
            increment_by: connectionDuration 
          }),
          updated_at: now.toISOString()
        })
        .eq('user_id', userId);
      
      return { success: true };
    } catch (error) {
      console.error('Error recording disconnection:', error);
      
      // For development, return success even if database operation fails
      if (process.env.NODE_ENV !== 'production') {
        return { success: true };
      }
      
      throw error;
    }
  } catch (error) {
    console.error('Error recording user disconnection:', error);
    return { success: false, error };
  }
}

/**
 * Get user metrics
 * @param userId User ID
 * @returns User metrics
 */
export async function getUserMetrics(userId: string): Promise<{ 
  success: boolean; 
  metrics?: UserMetrics;
  error?: any 
}> {
  try {
    console.log('Getting user metrics for user:', userId);
    
    const { data, error } = await supabaseAdmin
      .from('user_metrics')
      .select('*')
      .eq('user_id', userId)
      .single();
    
    if (error) {
      console.error('Error getting user metrics:', error);
      
      // If the error is that the table doesn't exist or no data found, return empty metrics
      if ((error.code === '42P01' || error.code === 'PGRST116') && process.env.NODE_ENV !== 'production') {
        return { 
          success: true, 
          metrics: {
            userId,
            firstConnectionTime: new Date().toISOString(),
            lastConnectionTime: new Date().toISOString(),
            totalConnections: 0,
            totalChatMessages: 0,
            totalConnectionDuration: 0,
            lastActiveTime: new Date().toISOString()
          }
        };
      }
      
      throw error;
    }
    
    if (!data) {
      return { 
        success: true, 
        metrics: {
          userId,
          firstConnectionTime: new Date().toISOString(),
          lastConnectionTime: new Date().toISOString(),
          totalConnections: 0,
          totalChatMessages: 0,
          totalConnectionDuration: 0,
          lastActiveTime: new Date().toISOString()
        }
      };
    }
    
    // Convert to camelCase
    const metrics: UserMetrics = {
      userId: data.user_id,
      firstConnectionTime: data.first_connection_time,
      lastConnectionTime: data.last_connection_time,
      totalConnections: data.total_connections,
      totalChatMessages: data.total_chat_messages || 0,
      totalConnectionDuration: data.total_connection_duration || 0,
      lastActiveTime: data.last_active_time,
      favoriteBrowser: data.favorite_browser,
      favoriteDeviceType: data.favorite_device_type
    };
    
    return { success: true, metrics };
  } catch (error) {
    console.error('Error getting user metrics:', error);
    return { success: false, error };
  }
}
