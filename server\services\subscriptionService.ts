import { supabaseAdmin } from '../supabase';
import { Subscription, SubscriptionStatus, PlanType, calculateEndDate } from '../models/subscription';

/**
 * Get a user's subscription
 * @param userId The user ID
 * @returns The subscription if found, null otherwise
 */
export async function getUserSubscription(userId: string): Promise<Subscription | null> {
  try {
    const { data, error } = await supabaseAdmin
      .from('subscriptions')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error) {
      console.error('Error getting user subscription:', error);
      return null;
    }

    return data as Subscription;
  } catch (error) {
    console.error('Exception during get user subscription:', error);
    return null;
  }
}

/**
 * Create or update a user's subscription
 * @param userId The user ID
 * @param planId The plan ID
 * @param stripeSubscriptionId The Stripe subscription ID (optional)
 * @param stripeCustomerId The Stripe customer ID (optional)
 * @param startDate The start date (optional, defaults to now)
 * @param endDate The end date (optional, calculated based on plan if not provided)
 * @param cancelAtPeriodEnd Whether to cancel at the end of the period (optional, defaults to false)
 * @returns The created or updated subscription if successful, null otherwise
 */
export async function createOrUpdateSubscription(
  userId: string,
  planId: string,
  stripeSubscriptionId?: string | null,
  stripeCustomerId?: string | null,
  startDate: Date = new Date(),
  endDate?: Date | null,
  cancelAtPeriodEnd: boolean = false
): Promise<Subscription | null> {
  try {
    // Get the current subscription if it exists
    const currentSubscription = await getUserSubscription(userId);

    // Handle the case where a user already has an active subscription
    if (currentSubscription && currentSubscription.status === SubscriptionStatus.ACTIVE) {
      console.log(`User ${userId} already has an active ${currentSubscription.plan_id} subscription`);

      // If purchasing the same plan, extend the subscription period
      if (currentSubscription.plan_id === planId) {
        console.log(`Extending ${planId} subscription for user ${userId}`);

        // Calculate the new end date based on the current end date
        let newEndDate: Date | null = null;

        if (currentSubscription.current_period_end) {
          // Start from the current end date
          const currentEndDate = new Date(currentSubscription.current_period_end);

          // Add the duration of the plan to the current end date
          if (planId === PlanType.PRO) {
            // Add 30 days
            newEndDate = new Date(currentEndDate.getTime() + 30 * 24 * 60 * 60 * 1000);
          } else if (planId === PlanType.ULTIMATE) {
            // Add 365 days
            newEndDate = new Date(currentEndDate.getTime() + 365 * 24 * 60 * 60 * 1000);
          }
        } else {
          // If there's no current end date, calculate a new one from now
          newEndDate = calculateEndDate(planId, startDate);
        }

        // Use the calculated end date
        endDate = newEndDate;

        console.log(`Extended subscription end date to ${endDate}`);
      } else {
        // If changing plans, start a new subscription period from today
        console.log(`Changing subscription from ${currentSubscription.plan_id} to ${planId} for user ${userId}`);

        // Calculate a new end date based on the new plan
        endDate = calculateEndDate(planId, startDate);

        console.log(`New subscription end date: ${endDate}`);
      }
    } else {
      // For new subscriptions or inactive subscriptions, calculate the end date if not provided
      endDate = endDate || calculateEndDate(planId, startDate);
    }

    // Call the stored procedure to set the user's subscription
    const { data, error } = await supabaseAdmin.rpc('set_user_subscription', {
      user_uuid: userId,
      plan: planId,
      stripe_sub_id: stripeSubscriptionId || null,
      stripe_cust_id: stripeCustomerId || null,
      period_start: startDate.toISOString(),
      period_end: endDate ? endDate.toISOString() : null,
      cancel_at_end: cancelAtPeriodEnd
    });

    if (error) {
      console.error('Error creating/updating subscription:', error);
      return null;
    }

    // Get the updated subscription
    return await getUserSubscription(userId);
  } catch (error) {
    console.error('Exception during create/update subscription:', error);
    return null;
  }
}

/**
 * Downgrade a user to the free plan
 * @param userId The user ID
 * @returns True if successful, false otherwise
 */
export async function downgradeToFreePlan(userId: string): Promise<boolean> {
  try {
    const { error } = await supabaseAdmin.rpc('downgrade_to_free_plan', {
      user_uuid: userId
    });

    if (error) {
      console.error('Error downgrading to free plan:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Exception during downgrade to free plan:', error);
    return false;
  }
}

/**
 * Upgrade a user from one plan to another
 * @param userId The user ID
 * @param newPlanId The new plan ID
 * @param stripeSubscriptionId The Stripe subscription ID (optional)
 * @param stripeCustomerId The Stripe customer ID (optional)
 * @returns The updated subscription if successful, null otherwise
 */
export async function upgradePlan(
  userId: string,
  newPlanId: string,
  stripeSubscriptionId?: string | null,
  stripeCustomerId?: string | null
): Promise<Subscription | null> {
  try {
    console.log(`Upgrading user ${userId} to plan ${newPlanId}`);

    // Get the current date as the start date
    const startDate = new Date();

    // Calculate the end date based on the new plan
    const endDate = calculateEndDate(newPlanId, startDate);

    // Update the subscription
    return await createOrUpdateSubscription(
      userId,
      newPlanId,
      stripeSubscriptionId,
      stripeCustomerId,
      startDate,
      endDate,
      false // Not set to cancel at period end
    );
  } catch (error) {
    console.error('Exception during plan upgrade:', error);
    return null;
  }
}

/**
 * Extend a user's current subscription
 * @param userId The user ID
 * @returns The updated subscription if successful, null otherwise
 */
export async function extendSubscription(userId: string): Promise<Subscription | null> {
  try {
    // Get the current subscription
    const currentSubscription = await getUserSubscription(userId);

    if (!currentSubscription) {
      console.error(`No subscription found for user ${userId}`);
      return null;
    }

    console.log(`Extending ${currentSubscription.plan_id} subscription for user ${userId}`);

    // Calculate the new end date based on the current end date
    let newEndDate: Date | null = null;

    if (currentSubscription.current_period_end) {
      // Start from the current end date
      const currentEndDate = new Date(currentSubscription.current_period_end);

      // Add the duration of the plan to the current end date
      if (currentSubscription.plan_id === PlanType.PRO) {
        // Add 30 days
        newEndDate = new Date(currentEndDate.getTime() + 30 * 24 * 60 * 60 * 1000);
      } else if (currentSubscription.plan_id === PlanType.ULTIMATE) {
        // Add 365 days
        newEndDate = new Date(currentEndDate.getTime() + 365 * 24 * 60 * 60 * 1000);
      }
    } else {
      // If there's no current end date, calculate a new one from now
      newEndDate = calculateEndDate(currentSubscription.plan_id, new Date());
    }

    // Update the subscription with the new end date
    return await createOrUpdateSubscription(
      userId,
      currentSubscription.plan_id,
      currentSubscription.stripe_subscription_id,
      currentSubscription.stripe_customer_id,
      new Date(currentSubscription.current_period_start || new Date()),
      newEndDate,
      currentSubscription.cancel_at_period_end
    );
  } catch (error) {
    console.error('Exception during subscription extension:', error);
    return null;
  }
}

/**
 * Check for expired subscriptions and downgrade them to the free plan
 * @returns The number of subscriptions that were downgraded
 */
export async function checkExpiredSubscriptions(): Promise<number> {
  try {
    // First, update the status of expired subscriptions
    await supabaseAdmin.rpc('check_expired_subscriptions');

    // Get all expired subscriptions
    const { data, error } = await supabaseAdmin
      .from('subscriptions')
      .select('*')
      .eq('status', SubscriptionStatus.EXPIRED);

    if (error) {
      console.error('Error getting expired subscriptions:', error);
      return 0;
    }

    // Downgrade each expired subscription to the free plan
    let downgradeCount = 0;
    for (const subscription of data as Subscription[]) {
      const success = await downgradeToFreePlan(subscription.user_id);
      if (success) {
        downgradeCount++;
      }
    }

    return downgradeCount;
  } catch (error) {
    console.error('Exception during check expired subscriptions:', error);
    return 0;
  }
}

/**
 * Initialize a free subscription for a new user
 * @param userId The user ID
 * @returns The created subscription if successful, null otherwise
 */
export async function initializeFreeSubscription(userId: string): Promise<Subscription | null> {
  return await createOrUpdateSubscription(userId, PlanType.FREE);
}
