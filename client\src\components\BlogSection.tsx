import { motion } from 'framer-motion';
import { BLOG_POSTS } from '@/lib/constants';
import { ArrowRight } from 'lucide-react';

export default function BlogSection() {
  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0, transition: { duration: 0.5 } }
  };

  const getTagColorClass = (color: string) => {
    switch (color) {
      case 'primary': return 'bg-primary/90 text-background';
      case 'secondary': return 'bg-secondary/90 text-background';
      case 'accent': return 'bg-accent/90 text-background';
      default: return 'bg-primary/90 text-background';
    }
  };

  return (
    <section id="blog" className="py-16 md:py-24 relative">
      {/* Background effects */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 left-1/2 transform -translate-x-1/2 w-[70%] h-[60%] bg-primary/10 rounded-full blur-[180px] animate-pulse-slow"></div>
        <div className="absolute bottom-1/4 left-1/2 transform -translate-x-1/2 w-[65%] h-[55%] bg-accent/10 rounded-full blur-[150px] animate-pulse-slow-reverse"></div>
      </div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="text-center mb-20"
      >
        <h2 className="font-inter text-5xl md:text-6xl font-bold tracking-tight">
          <span className="bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary">The Blog</span>
        </h2>
        <p className="font-inter text-lg md:text-xl text-muted-foreground mt-6 max-w-2xl mx-auto leading-relaxed">
          Our AI doesn't just analyze websites—it writes scathing reviews that expose the digital world's darkest secrets. No website is safe from our judgment.
        </p>
      </motion.div>

      <motion.div
        className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 md:gap-10 max-w-[98%] lg:max-w-[100%] xl:max-w-[102%] mx-auto"
        variants={container}
        initial="hidden"
        whileInView="show"
        viewport={{ once: true, amount: 0.2 }}
      >
        {BLOG_POSTS.map((post) => (
          <motion.article
            key={post.id}
            className="bg-card/50 backdrop-blur-sm border border-muted/30 rounded-xl overflow-hidden group relative hover:border-primary/30 transition-all duration-500 h-full flex flex-col shadow-md hover:shadow-xl"
            variants={item}
            whileHover={{
              boxShadow: "0 10px 30px rgba(0, 166, 192, 0.2), 0 0 0 1px rgba(0, 166, 192, 0.1)"
            }}
          >
            <div className="h-56 bg-background relative overflow-hidden">
              {/* Gradient overlay */}
              <div className="absolute inset-0 bg-gradient-to-br from-primary/10 to-secondary/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500 z-10 pointer-events-none"></div>

              <img
                src={post.image}
                alt={post.title}
                className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-105"
              />

              <div className={`absolute top-3 right-3 ${getTagColorClass(post.tagColor)} text-xs font-bold px-3 py-1.5 rounded-full shadow-lg z-20 font-inter`}>
                {post.tag}
              </div>
            </div>

            <div className="p-6 md:p-8 flex flex-col flex-grow">
              <h3 className="font-inter text-xl md:text-2xl font-semibold mb-3 line-clamp-2 group-hover:text-primary transition-colors duration-300">{post.title}</h3>

              <div className="text-muted-foreground text-sm mb-4 font-inter flex items-center">
                <div className="w-6 h-6 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-full mr-2 flex items-center justify-center shadow-sm">
                  <div className="w-5 h-5 bg-background rounded-full flex items-center justify-center">
                    <span className="text-primary text-xs">AI</span>
                  </div>
                </div>
                <span className="text-primary font-medium">browzy AI</span>
                <span className="mx-2">•</span>
                <span>{post.date}</span>
              </div>

              <div className="bg-background/50 backdrop-blur-sm p-4 rounded-lg border border-muted/20 mb-4">
                <p className="text-muted-foreground font-inter text-sm line-clamp-3">
                  {post.excerpt}
                </p>
              </div>

              <div className="mt-auto pt-4 flex justify-between items-center">
                <a
                  href={`#blog/${post.slug}`}
                  className="text-primary font-inter text-sm font-medium hover:text-primary/80 transition-colors duration-300 flex items-center group/link"
                >
                  Read full article
                  <ArrowRight className="ml-2 h-4 w-4 group-hover/link:translate-x-1 transition-transform duration-300" />
                </a>

                <span className="font-mono text-xs text-muted-foreground bg-muted/30 px-2 py-1 rounded">
                  {post.commandText}
                </span>
              </div>
            </div>
          </motion.article>
        ))}
      </motion.div>

      <div className="text-center mt-16">
        <div className="relative group inline-block">
          <div className="absolute -inset-0.5 bg-gradient-to-r from-primary/40 to-secondary/40 rounded-xl blur opacity-30 group-hover:opacity-100 transition duration-500 group-hover:duration-200"></div>
          <a
            href="#"
            className="relative inline-flex items-center px-8 py-4 bg-card border border-primary/20 rounded-xl font-inter font-medium text-foreground hover:text-primary transition-all duration-300"
          >
            View All Blog Posts
            <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform duration-300" />
          </a>
        </div>
      </div>

      <div className="mt-24 mb-16 relative">
        <div className="h-px w-full bg-gradient-to-r from-transparent via-primary/40 to-transparent"></div>
        <div className="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 w-20 h-20">
          <div className="absolute inset-0 bg-gradient-to-r from-primary/30 to-accent/30 rounded-full blur-xl animate-pulse-slow"></div>
          <div className="absolute inset-3 bg-gradient-to-r from-primary/20 to-accent/20 rounded-full blur-md animate-pulse-slow-reverse"></div>
        </div>
      </div>
    </section>
  );
}
