/**
 * Subscription model interface
 */
export interface Subscription {
  id: string;
  user_id: string;
  plan_id: string;
  stripe_subscription_id: string | null;
  stripe_customer_id: string | null;
  status: SubscriptionStatus;
  current_period_start: Date | null;
  current_period_end: Date | null;
  cancel_at_period_end: boolean;
  created_at: Date;
  updated_at: Date;
}

/**
 * Subscription status enum
 */
export enum SubscriptionStatus {
  ACTIVE = 'active',
  CANCELED = 'canceled',
  EXPIRED = 'expired',
  PAST_DUE = 'past_due',
  UNPAID = 'unpaid',
  INCOMPLETE = 'incomplete',
  INCOMPLETE_EXPIRED = 'incomplete_expired',
  TRIALING = 'trialing'
}

/**
 * Plan type enum
 */
export enum PlanType {
  FREE = 'free',
  PRO = 'pro',
  ULTIMATE = 'ultimate'
}

/**
 * Plan details interface
 */
export interface PlanDetails {
  id: PlanType;
  name: string;
  description: string;
  price: number;
  currency: string;
  interval: 'month' | 'year';
  priceId: string;
  features: Array<{ text: string, included: boolean }>;
}

/**
 * Get the duration of a plan in milliseconds
 * @param planId The plan ID
 * @returns The duration in milliseconds
 */
export function getPlanDuration(planId: string): number {
  switch (planId) {
    case PlanType.PRO:
      return 30 * 24 * 60 * 60 * 1000; // 30 days in milliseconds
    case PlanType.ULTIMATE:
      return 365 * 24 * 60 * 60 * 1000; // 365 days in milliseconds
    case PlanType.FREE:
    default:
      return 0; // Free plan doesn't expire
  }
}

/**
 * Calculate the end date for a subscription based on the plan type and start date
 * @param planId The plan ID
 * @param startDate The start date
 * @returns The end date
 */
export function calculateEndDate(planId: string, startDate: Date = new Date()): Date | null {
  if (planId === PlanType.FREE) {
    return null; // Free plan doesn't expire
  }

  const duration = getPlanDuration(planId);
  const endDate = new Date(startDate.getTime() + duration);
  
  return endDate;
}

/**
 * Check if a subscription is expired
 * @param subscription The subscription to check
 * @returns True if the subscription is expired, false otherwise
 */
export function isSubscriptionExpired(subscription: Subscription): boolean {
  if (subscription.plan_id === PlanType.FREE) {
    return false; // Free plan doesn't expire
  }

  if (!subscription.current_period_end) {
    return false; // No end date means it doesn't expire
  }

  return new Date() > new Date(subscription.current_period_end);
}
