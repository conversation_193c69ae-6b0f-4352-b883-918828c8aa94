import { useState, useEffect } from 'react';
import { Menu, User, LogOut } from 'lucide-react';
import { But<PERSON> } from "@/components/ui/button";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { ThemeToggle } from "@/components/ThemeToggle";
import { Link, useLocation } from "wouter";
import { useAuth } from "@/contexts/AuthContext";
import { signOut } from "@/lib/supabase";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

const navLinks = [
  { href: "/features", label: "Features" },
  { href: "#integrations", label: "Integrations" },
  { href: "#contact", label: "Contact" }
];

export default function HeaderWithAuth() {
  const [isScrolled, setIsScrolled] = useState(false);
  const [, setLocation] = useLocation();
  const { user, isAuthenticated, isLoading } = useAuth();
  const [isSigningOut, setIsSigningOut] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };

    window.addEventListener('scroll', handleScroll);

    // Clean up
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  // Get user initials for avatar fallback
  const getInitials = () => {
    if (!user?.email) return 'U';
    return user.email.charAt(0).toUpperCase();
  };

  const handleSignOut = async () => {
    setIsSigningOut(true);
    try {
      await signOut();
      setLocation('/');
    } catch (error) {
      console.error('Error signing out:', error);
    } finally {
      setIsSigningOut(false);
    }
  };

  return (
    <nav className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300
      ${isScrolled
        ? 'bg-background/95 backdrop-blur-md border-b border-border/50 py-3'
        : 'bg-background/80 backdrop-blur-md py-4'}`}>
      <div className="max-w-[94%] lg:max-w-[92%] xl:max-w-[88%] mx-auto">
        <div className="flex justify-between items-center">
          {/* Logo */}
          <div className="flex items-center space-x-3">
            <Link href="/">
              <div className="flex items-center space-x-3 cursor-pointer">
                <div className="h-8 w-8 rounded-full flex items-center justify-center shadow-sm overflow-hidden">
                  <img src="/images/browzy-logo.png" alt="Browzy Logo" className="h-full w-full object-cover" />
                </div>
                <span className="font-heading font-semibold text-xl text-foreground">
                  browzy<span className="text-primary font-bold">ai</span>
                </span>
              </div>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center">
            <div className="mr-10 flex gap-10">
              {navLinks.map((link) => (
                link.href.startsWith('/') ? (
                  <Link
                    key={link.href}
                    href={link.href}
                    className="text-foreground hover:text-primary transition-colors duration-200 font-medium relative group"
                  >
                    {link.label}
                    <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-full"></span>
                  </Link>
                ) : (
                  <a
                    key={link.href}
                    href={link.href}
                    className="text-foreground hover:text-primary transition-colors duration-200 font-medium relative group"
                  >
                    {link.label}
                    <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-full"></span>
                  </a>
                )
              ))}
            </div>

            <div className="flex items-center gap-3">
              <ThemeToggle />

              {isLoading ? (
                <div className="w-8 h-8 rounded-full bg-muted animate-pulse"></div>
              ) : isAuthenticated ? (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="relative h-8 w-8 rounded-full">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={user?.user_metadata?.avatar_url || ''} alt={user?.email || 'User'} />
                        <AvatarFallback>{getInitials()}</AvatarFallback>
                      </Avatar>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="w-56" align="end" forceMount>
                    <DropdownMenuLabel className="font-normal">
                      <div className="flex flex-col space-y-1">
                        <p className="text-sm font-medium leading-none">Account</p>
                        <p className="text-xs leading-none text-muted-foreground">
                          {user?.email}
                        </p>
                      </div>
                    </DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => setLocation('/profile')}>
                      <User className="mr-2 h-4 w-4" />
                      <span>Profile</span>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={handleSignOut} disabled={isSigningOut}>
                      <LogOut className="mr-2 h-4 w-4" />
                      <span>{isSigningOut ? 'Signing out...' : 'Sign out'}</span>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              ) : (
                <>
                  <Button
                    variant="outline"
                    onClick={() => setLocation('/signin')}
                  >
                    Sign In
                  </Button>
                  <Button
                    onClick={() => setLocation('/signup')}
                  >
                    Sign Up
                  </Button>
                </>
              )}
            </div>
          </div>

          {/* Mobile Menu */}
          <div className="md:hidden">
            <Sheet>
              <SheetTrigger asChild>
                <Button variant="ghost" size="icon" className="text-foreground hover:text-primary transition">
                  <Menu size={24} />
                </Button>
              </SheetTrigger>
              <SheetContent side="right" className="bg-background/95 backdrop-blur-md border-l border-border/50">
                <div className="flex flex-col space-y-6 mt-10">
                  {navLinks.map((link) => (
                    link.href.startsWith('/') ? (
                      <Link
                        key={link.href}
                        href={link.href}
                        className="text-foreground hover:text-primary transition font-medium py-3 px-4 border-b border-border/30"
                      >
                        {link.label}
                      </Link>
                    ) : (
                      <a
                        key={link.href}
                        href={link.href}
                        className="text-foreground hover:text-primary transition font-medium py-3 px-4 border-b border-border/30"
                      >
                        {link.label}
                      </a>
                    )
                  ))}
                  <div className="flex items-center justify-between py-3 px-4 border-b border-border/30">
                    <span className="font-medium">Theme</span>
                    <ThemeToggle />
                  </div>
                  <div className="flex flex-col gap-4 pt-6 px-4">
                    {isAuthenticated ? (
                      <>
                        <Button
                          onClick={() => setLocation('/profile')}
                          className="w-full"
                        >
                          <User className="mr-2 h-4 w-4" />
                          Profile
                        </Button>
                        <Button
                          variant="outline"
                          onClick={handleSignOut}
                          disabled={isSigningOut}
                          className="w-full"
                        >
                          <LogOut className="mr-2 h-4 w-4" />
                          {isSigningOut ? 'Signing out...' : 'Sign out'}
                        </Button>
                      </>
                    ) : (
                      <>
                        <Button
                          variant="outline"
                          onClick={() => setLocation('/signin')}
                          className="w-full"
                        >
                          Sign In
                        </Button>
                        <Button
                          onClick={() => setLocation('/signup')}
                          className="w-full"
                        >
                          Sign Up
                        </Button>
                      </>
                    )}
                  </div>
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>
    </nav>
  );
}
