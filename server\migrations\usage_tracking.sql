-- Create usage_tracking table
CREATE TABLE IF NOT EXISTS usage_tracking (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  request_type VARCHAR(50) NOT NULL, -- 'chat', 'search', etc.
  timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  request_count INTEGER DEFAULT 1,
  plan_id VARCHAR(20) DEFAULT 'free',
  billing_period_start TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  billing_period_end TIMESTAMP WITH TIME ZONE
);

-- Create index on user_id for faster lookups
CREATE INDEX IF NOT EXISTS idx_usage_tracking_user_id ON usage_tracking(user_id);

-- <PERSON>reate index on timestamp for sorting and filtering
CREATE INDEX IF NOT EXISTS idx_usage_tracking_timestamp ON usage_tracking(timestamp);

-- Create index on billing period for filtering
CREATE INDEX IF NOT EXISTS idx_usage_tracking_billing_period ON usage_tracking(billing_period_start, billing_period_end);

-- Create daily_usage_summary view for quick access to daily usage
CREATE OR REPLACE VIEW daily_usage_summary AS
SELECT 
  user_id,
  DATE(timestamp) as usage_date,
  SUM(request_count) as daily_requests
FROM 
  usage_tracking
GROUP BY 
  user_id, DATE(timestamp);

-- Create monthly_usage_summary view for quick access to monthly usage
CREATE OR REPLACE VIEW monthly_usage_summary AS
SELECT 
  user_id,
  DATE_TRUNC('month', timestamp) as month_start,
  SUM(request_count) as monthly_requests
FROM 
  usage_tracking
GROUP BY 
  user_id, DATE_TRUNC('month', timestamp);

-- Create RLS policies for usage_tracking

-- Enable RLS on usage_tracking
ALTER TABLE usage_tracking ENABLE ROW LEVEL SECURITY;

-- Create policy to allow users to see only their own usage data
CREATE POLICY select_own_usage ON usage_tracking
  FOR SELECT USING (auth.uid() = user_id);

-- Create policy to allow users to insert their own usage data
CREATE POLICY insert_own_usage ON usage_tracking
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Create policy to allow users to update their own usage data
CREATE POLICY update_own_usage ON usage_tracking
  FOR UPDATE USING (auth.uid() = user_id);

-- Create policy to allow users to delete their own usage data
CREATE POLICY delete_own_usage ON usage_tracking
  FOR DELETE USING (auth.uid() = user_id);
