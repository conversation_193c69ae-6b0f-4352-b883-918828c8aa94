import { motion } from 'framer-motion';

export default function FloatingElements() {
  // Array of floating elements with different properties
  const elements = [
    {
      shape: 'circle',
      size: 'w-16 h-16 md:w-20 md:h-20',
      position: 'top-[10%] left-[5%]',
      color: 'bg-primary/10',
      blur: 'blur-xl',
      animation: {
        y: [-15, 15, -15],
        x: [-5, 5, -5],
        transition: {
          y: { repeat: Infinity, duration: 6, ease: 'easeInOut' },
          x: { repeat: Infinity, duration: 8, ease: 'easeInOut' }
        }
      }
    },
    {
      shape: 'circle',
      size: 'w-24 h-24 md:w-32 md:h-32',
      position: 'top-[20%] right-[10%]',
      color: 'bg-accent/10',
      blur: 'blur-2xl',
      animation: {
        y: [10, -10, 10],
        x: [5, -5, 5],
        transition: {
          y: { repeat: Infinity, duration: 7, ease: 'easeInOut' },
          x: { repeat: Infinity, duration: 9, ease: 'easeInOut' }
        }
      }
    },
    {
      shape: 'rounded-md',
      size: 'w-20 h-20 md:w-24 md:h-24',
      position: 'bottom-[15%] left-[15%]',
      color: 'bg-secondary/10',
      blur: 'blur-xl',
      animation: {
        y: [-10, 10, -10],
        rotate: [0, 10, 0],
        transition: {
          y: { repeat: Infinity, duration: 8, ease: 'easeInOut' },
          rotate: { repeat: Infinity, duration: 10, ease: 'easeInOut' }
        }
      }
    },
    {
      shape: 'rounded-full',
      size: 'w-32 h-32 md:w-40 md:h-40',
      position: 'bottom-[10%] right-[5%]',
      color: 'bg-primary/5',
      blur: 'blur-3xl',
      animation: {
        y: [15, -15, 15],
        scale: [1, 1.1, 1],
        transition: {
          y: { repeat: Infinity, duration: 9, ease: 'easeInOut' },
          scale: { repeat: Infinity, duration: 7, ease: 'easeInOut' }
        }
      }
    },
    {
      shape: 'rounded-md',
      size: 'w-12 h-12 md:w-16 md:h-16',
      position: 'top-[40%] left-[30%]',
      color: 'bg-accent/8',
      blur: 'blur-lg',
      animation: {
        y: [8, -8, 8],
        x: [-8, 8, -8],
        rotate: [0, -10, 0],
        transition: {
          y: { repeat: Infinity, duration: 5, ease: 'easeInOut' },
          x: { repeat: Infinity, duration: 6, ease: 'easeInOut' },
          rotate: { repeat: Infinity, duration: 7, ease: 'easeInOut' }
        }
      }
    }
  ];

  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {elements.map((element, index) => (
        <motion.div
          key={index}
          className={`absolute ${element.position} ${element.size} ${element.shape} ${element.color} ${element.blur}`}
          animate={element.animation}
        />
      ))}
    </div>
  );
}
