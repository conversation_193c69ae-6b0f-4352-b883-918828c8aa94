const fs = require('fs');
const path = require('path');
const { supabaseAdmin } = require('../supabase');

async function runMigrations() {
  try {
    console.log('Running database migrations...');

    // Get all SQL files in the migrations directory
    const migrationsDir = path.join(__dirname);
    const sqlFiles = fs.readdirSync(migrationsDir)
      .filter(file => file.endsWith('.sql'))
      .sort(); // Sort to ensure consistent order

    console.log(`Found ${sqlFiles.length} migration files`);

    // Run each migration
    for (const sqlFile of sqlFiles) {
      console.log(`Running migration: ${sqlFile}`);
      
      // Read the SQL file
      const sqlPath = path.join(migrationsDir, sqlFile);
      const sql = fs.readFileSync(sqlPath, 'utf8');
      
      // Execute the SQL
      const { error } = await supabaseAdmin.rpc('exec_sql', { sql });
      
      if (error) {
        console.error(`Error running migration ${sqlFile}:`, error);
        throw error;
      }
      
      console.log(`Migration ${sqlFile} completed successfully`);
    }

    console.log('All migrations completed successfully');
  } catch (error) {
    console.error('Error running migrations:', error);
    process.exit(1);
  }
}

// Run the migrations
runMigrations();
