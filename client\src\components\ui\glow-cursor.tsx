import { useEffect, useState, useRef } from 'react';

interface GlowCursorProps {
  color?: string;
  size?: number;
  blur?: number;
  opacity?: number;
  delay?: number;
  pulseSpeed?: number;
}

interface TrailPoint {
  x: number;
  y: number;
  age: number;
  opacity: number;
  size: number;
}

export function GlowCursor({
  color = 'rgba(0, 166, 192, 0.3)',
  size = 30,
  blur = 18,
  opacity = 0.6,
  delay = 40,
  pulseSpeed = 2000
}: GlowCursorProps) {
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [visible, setVisible] = useState(false);
  const [scale, setScale] = useState(1);
  const [isHoveringInteractive, setIsHoveringInteractive] = useState(false);
  const [trail, setTrail] = useState<TrailPoint[]>([]);
  const pulseTimerRef = useRef<number | null>(null);
  const targetRef = useRef({ x: 0, y: 0 });
  const animationFrameRef = useRef<number | null>(null);
  const lastPositionRef = useRef({ x: 0, y: 0 });
  const lastTrailAddTimeRef = useRef(0);

  // Smooth cursor movement with lerp (linear interpolation)
  const lerp = (start: number, end: number, factor: number) => {
    return start + (end - start) * factor;
  };

  // Handle mouse movement with smooth animation
  useEffect(() => {
    const updateTarget = (e: MouseEvent) => {
      targetRef.current = { x: e.clientX, y: e.clientY };
      if (!visible) setVisible(true);
    };

    const handleMouseLeave = () => {
      setVisible(false);
    };

    const handleMouseEnter = () => {
      setVisible(true);
    };

    // Check if hovering over interactive elements
    const checkInteractiveElements = () => {
      const element = document.elementFromPoint(targetRef.current.x, targetRef.current.y);
      const interactiveElements = ['A', 'BUTTON', 'INPUT', 'SELECT', 'TEXTAREA'];
      const isInteractive = element && (
        interactiveElements.includes(element.tagName) ||
        element.classList.contains('interactive') ||
        element.closest('button') ||
        element.closest('a')
      );

      setIsHoveringInteractive(!!isInteractive);
    };

    // Update trail points
    const updateTrail = () => {
      const now = Date.now();
      const distance = Math.sqrt(
        Math.pow(position.x - lastPositionRef.current.x, 2) +
        Math.pow(position.y - lastPositionRef.current.y, 2)
      );

      // Only add trail points if cursor has moved enough and enough time has passed
      if (distance > 5 && now - lastTrailAddTimeRef.current > 40) {
        const newTrailPoint: TrailPoint = {
          x: position.x,
          y: position.y,
          age: 0,
          opacity: 0.7,
          size: size * 0.7
        };

        setTrail(prevTrail => {
          // Add new point and limit trail length
          const updatedTrail = [newTrailPoint, ...prevTrail.slice(0, 8)];

          // Update existing trail points
          return updatedTrail.map((point, index) => {
            if (index === 0) return point;
            return {
              ...point,
              age: point.age + 1,
              opacity: Math.max(0, point.opacity - 0.08),
              size: Math.max(size * 0.2, point.size - size * 0.05)
            };
          }).filter(point => point.opacity > 0.05);
        });

        lastPositionRef.current = { x: position.x, y: position.y };
        lastTrailAddTimeRef.current = now;
      }
    };

    // Animate cursor position with smooth movement
    const animateCursor = () => {
      // Apply easing with lerp
      const easeFactor = 0.15;
      const newX = lerp(position.x, targetRef.current.x, easeFactor);
      const newY = lerp(position.y, targetRef.current.y, easeFactor);

      if (Math.abs(newX - position.x) > 0.01 || Math.abs(newY - position.y) > 0.01) {
        setPosition({ x: newX, y: newY });
        updateTrail();
      }

      checkInteractiveElements();
      animationFrameRef.current = requestAnimationFrame(animateCursor);
    };

    window.addEventListener('mousemove', updateTarget);
    document.addEventListener('mouseleave', handleMouseLeave);
    document.addEventListener('mouseenter', handleMouseEnter);

    animationFrameRef.current = requestAnimationFrame(animateCursor);

    return () => {
      window.removeEventListener('mousemove', updateTarget);
      document.removeEventListener('mouseleave', handleMouseLeave);
      document.removeEventListener('mouseenter', handleMouseEnter);

      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [visible, position.x, position.y, size]);

  // Create pulsing effect
  useEffect(() => {
    const startTime = Date.now();

    const animatePulse = () => {
      const elapsed = Date.now() - startTime;
      const pulseFactor = Math.sin(elapsed / pulseSpeed * Math.PI) * 0.2 + 1;
      setScale(pulseFactor);

      pulseTimerRef.current = requestAnimationFrame(animatePulse);
    };

    pulseTimerRef.current = requestAnimationFrame(animatePulse);

    return () => {
      if (pulseTimerRef.current) {
        cancelAnimationFrame(pulseTimerRef.current);
      }
    };
  }, [pulseSpeed]);

  // Calculate interactive state styles
  const getInteractiveStyles = () => {
    if (isHoveringInteractive) {
      return {
        size: size * 0.6,
        opacity: opacity * 1.5,
        scale: scale * 1.2,
        blur: blur * 0.7,
        color: color.replace(/[\d.]+\)$/, '0.7)'),
      };
    }
    return {
      size,
      opacity,
      scale,
      blur,
      color,
    };
  };

  const interactiveStyles = getInteractiveStyles();

  // Get color with adjusted opacity
  const getColorWithOpacity = (baseColor: string, opacityValue: number) => {
    // Extract RGB values from rgba string
    const rgbaMatch = baseColor.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*([\d.]+))?\)/);
    if (rgbaMatch) {
      const [, r, g, b] = rgbaMatch;
      return `rgba(${r}, ${g}, ${b}, ${opacityValue})`;
    }
    return baseColor;
  };

  return (
    <>
      {/* Trail effect */}
      {trail.map((point, index) => (
        <div
          key={`trail-${index}`}
          className="fixed pointer-events-none z-50"
          style={{
            opacity: visible ? point.opacity * interactiveStyles.opacity : 0,
            left: `${point.x}px`,
            top: `${point.y}px`,
            width: `${point.size}px`,
            height: `${point.size}px`,
            transform: `translate(-50%, -50%)`,
            borderRadius: '50%',
            backgroundColor: getColorWithOpacity(interactiveStyles.color, point.opacity),
            filter: `blur(${interactiveStyles.blur * 0.8}px)`,
          }}
        />
      ))}

      {/* Main glow cursor */}
      <div
        className="fixed pointer-events-none z-50 transition-opacity duration-300"
        style={{
          opacity: visible ? interactiveStyles.opacity : 0,
          left: `${position.x}px`,
          top: `${position.y}px`,
          width: `${interactiveStyles.size}px`,
          height: `${interactiveStyles.size}px`,
          transform: `translate(-50%, -50%) scale(${interactiveStyles.scale})`,
          borderRadius: '50%',
          backgroundColor: interactiveStyles.color,
          filter: `blur(${interactiveStyles.blur}px)`,
          transition: `opacity 0.3s ease, width 0.3s ease, height 0.3s ease, background-color 0.3s ease, filter 0.3s ease`,
        }}
      />

      {/* Secondary smaller, sharper glow for more definition */}
      <div
        className="fixed pointer-events-none z-50 transition-opacity duration-300"
        style={{
          opacity: visible ? interactiveStyles.opacity * 1.5 : 0,
          left: `${position.x}px`,
          top: `${position.y}px`,
          width: `${interactiveStyles.size * 0.4}px`,
          height: `${interactiveStyles.size * 0.4}px`,
          transform: `translate(-50%, -50%) scale(${interactiveStyles.scale * 0.8})`,
          borderRadius: '50%',
          backgroundColor: interactiveStyles.color.replace(/[\d.]+\)$/, '0.8)'),
          filter: `blur(${interactiveStyles.blur * 0.3}px)`,
          transition: `opacity 0.3s ease, width 0.3s ease, height 0.3s ease, background-color 0.3s ease, filter 0.3s ease`,
        }}
      />

      {/* Outer ring effect */}
      <div
        className="fixed pointer-events-none z-50 transition-opacity duration-300"
        style={{
          opacity: visible ? interactiveStyles.opacity * 0.4 : 0,
          left: `${position.x}px`,
          top: `${position.y}px`,
          width: `${interactiveStyles.size * 1.8}px`,
          height: `${interactiveStyles.size * 1.8}px`,
          transform: `translate(-50%, -50%) scale(${interactiveStyles.scale * 1.1})`,
          borderRadius: '50%',
          border: `1px solid ${interactiveStyles.color.replace(/[\d.]+\)$/, '0.3)')}`,
          boxShadow: `0 0 10px ${interactiveStyles.color.replace(/[\d.]+\)$/, '0.2)')}`,
          transition: `opacity 0.3s ease, width 0.3s ease, height 0.3s ease, border 0.3s ease`,
        }}
      />

      {/* Tiny dot at center for precision */}
      <div
        className="fixed pointer-events-none z-50 transition-opacity duration-300"
        style={{
          opacity: visible ? (isHoveringInteractive ? 0.9 : 0.6) : 0,
          left: `${position.x}px`,
          top: `${position.y}px`,
          width: `${isHoveringInteractive ? 4 : 2}px`,
          height: `${isHoveringInteractive ? 4 : 2}px`,
          transform: `translate(-50%, -50%)`,
          borderRadius: '50%',
          backgroundColor: isHoveringInteractive ? 'rgba(255, 255, 255, 0.9)' : 'rgba(255, 255, 255, 0.7)',
          boxShadow: isHoveringInteractive ? '0 0 4px rgba(255, 255, 255, 0.5)' : 'none',
          transition: `opacity 0.3s ease, width 0.3s ease, height 0.3s ease, background-color 0.3s ease, box-shadow 0.3s ease`,
        }}
      />
    </>
  );
}
