# browzy ai - Supercharge Your Browser with AI

browzy ai is a browser extension powered by AI that enhances your browsing experience with intelligent insights and analysis. It provides real-time content analysis, smart suggestions, and personalized browsing assistance.

## Features

- **Content Analysis**: Instantly analyze web pages for key information and insights
- **Smart Suggestions**: Get personalized recommendations based on your browsing habits
- **Cross-Browser Support**: Works with Chrome, Firefox, and other major browsers
- **User Dashboard**: Track your browsing analytics and manage your preferences
- **Multiple Plans**: Choose from Free, Pro, and Ultimate plans to suit your needs

## Getting Started

### Prerequisites

- Node.js (v16 or higher)
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone https://github.com/Arkit-k/browzy-full-stack-.git
cd browzy-full-stack-
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
   - Create a `.env` file in the root directory
   - Create a `.env` file in the `client` directory
   - Create a `.env` file in the `server` directory
   - Use the `.env.example` files in each directory as templates
   - Make sure to add your Supabase credentials and other required variables

### Development

#### Using Docker (Recommended)

The easiest way to run the application is using Docker:

```bash
npm run docker:dev
```

Or run the batch file directly:

```bash
docker-dev.bat
```

This will:
- Build and start both the server and client in Docker containers
- Configure the frontend to proxy API requests to the backend
- Mount your local files as volumes for hot reloading

To stop the Docker containers:

```bash
npm run docker:stop
```

#### Without Docker

To start both the frontend and backend servers together:

```bash
npm run start-dev
```

This will:
- Start the backend server on port 5000
- Start the frontend development server on port 5173
- Configure the frontend to proxy API requests to the backend

Alternatively, you can run them separately:

- Backend only:
```bash
npm run dev
```

- Frontend only:
```bash
npm run frontend
```

### Production

#### Using Docker (Recommended)

To build and run the application in production mode:

```bash
npm run docker:prod
```

Or run the batch file directly:

```bash
docker-prod.bat
```

This will:
- Build the application in production mode
- Run it in a Docker container
- Expose port 5000 for the application

#### Without Docker

To build the application:

```bash
npm run build
```

This will:
- Build the frontend into the `dist/public` directory
- Bundle the backend into the `dist` directory

To run the built application:

```bash
npm start
```

## Server-Client Connection

The application uses a full-stack architecture:

1. **Frontend**: React application served by Vite during development
2. **Backend**: Express server that handles API requests and authentication
3. **Database**: Supabase for data storage and authentication

### API Endpoints

The backend provides several API endpoints:

- **Authentication**:
  - `POST /api/auth/signin`: Sign in with email and password
  - `POST /api/auth/signup`: Sign up with email and password
  - `POST /api/auth/google`: Sign in with Google
  - `POST /api/auth/signout`: Sign out
  - `POST /api/auth/refresh`: Refresh authentication token
  - `POST /api/auth/verify`: Verify authentication token

- **User Management**:
  - `GET /api/user/profile`: Get user profile
  - `PUT /api/user/preferences`: Update user preferences

- **Other**:
  - `GET /api/status`: Check API status
  - `POST /api/contact`: Submit contact form

### Testing the Connection

You can test the connection between the frontend and backend using the API Test component in the Dashboard's Settings section. This will verify that the frontend can communicate with the backend API.

## Authentication Flow

The application uses Supabase for authentication:

1. User signs in through the frontend
2. Backend verifies credentials with Supabase
3. Backend sets an HTTP-only cookie for session persistence
4. Frontend stores a token in localStorage for client-side access
5. Authenticated requests include the token in the Authorization header

## Folder Structure

- `client/`: Frontend React application
- `server/`: Backend Express server
- `shared/`: Shared code between frontend and backend
- `dist/`: Production build output

## Security

### Environment Variables

This project uses environment variables for configuration. Make sure to:

- Never commit `.env` files to the repository
- Use `.env.example` files as templates
- Keep sensitive information like API keys and database credentials secure
- Rotate keys if they are accidentally exposed

### Authentication

The application uses Supabase for secure authentication:
- Passwords are never stored in plaintext
- Authentication tokens are stored securely
- HTTP-only cookies are used for session management
- CSRF protection is implemented

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Contact

For questions or support, please contact the browzy ai team.
