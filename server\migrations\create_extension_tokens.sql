-- Create extension_tokens table
CREATE TABLE IF NOT EXISTS extension_tokens (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  token VARCHAR(20) NOT NULL UNIQUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_used TIMESTAMP WITH TIME ZONE,
  CONSTRAINT token_length CHECK (char_length(token) = 9)
);

-- Create index on user_id for faster lookups
CREATE INDEX IF NOT EXISTS idx_extension_tokens_user_id ON extension_tokens(user_id);

-- Create index on token for faster validation
CREATE INDEX IF NOT EXISTS idx_extension_tokens_token ON extension_tokens(token);

-- Enable RLS on extension_tokens
ALTER TABLE extension_tokens ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist (to avoid errors)
DROP POLICY IF EXISTS select_own_tokens ON extension_tokens;
DROP POLICY IF EXISTS insert_own_tokens ON extension_tokens;
DROP POLICY IF EXISTS update_own_tokens ON extension_tokens;
DROP POLICY IF EXISTS delete_own_tokens ON extension_tokens;

-- Create policy to allow users to see only their own tokens
CREATE POLICY select_own_tokens ON extension_tokens
  FOR SELECT USING (auth.uid() = user_id);

-- Create policy to allow users to insert their own tokens
CREATE POLICY insert_own_tokens ON extension_tokens
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Create policy to allow users to update their own tokens
CREATE POLICY update_own_tokens ON extension_tokens
  FOR UPDATE USING (auth.uid() = user_id);

-- Create policy to allow users to delete their own tokens
CREATE POLICY delete_own_tokens ON extension_tokens
  FOR DELETE USING (auth.uid() = user_id);
