import { useEffect, useState, useRef } from 'react';

interface BubbleProps {
  color?: string;
  maxBubbles?: number;
  minSize?: number;
  maxSize?: number;
  bubbleLifetime?: number;
  riseSpeed?: number;
  wobbleStrength?: number;
}

interface Bubble {
  id: number;
  x: number;
  y: number;
  size: number;
  opacity: number;
  lifetime: number;
  maxLifetime: number;
  wobbleSpeed: number;
  wobbleAmount: number;
  riseSpeed: number;
  color: string;
  borderColor: string;
  borderWidth: number;
}

export function Bubbles({
  color = 'rgba(0, 166, 192, 0.3)',
  maxBubbles = 40,
  minSize = 5,
  maxSize = 35,
  bubbleLifetime = 4000,
  riseSpeed = 1.5,
  wobbleStrength = 1.2
}: BubbleProps) {
  const [bubbles, setBubbles] = useState<Bubble[]>([]);
  const containerRef = useRef<HTMLDivElement>(null);
  const bubbleIdRef = useRef(0);
  const lastMousePosRef = useRef({ x: 0, y: 0 });
  const animationFrameRef = useRef<number>(0);

  // Handle mouse movement and create bubbles
  useEffect(() => {
    if (!containerRef.current) return;

    const createBubble = (x: number, y: number) => {
      const id = bubbleIdRef.current++;

      // Random bubble size
      const size = Math.random() * (maxSize - minSize) + minSize;

      // Random lifetime
      const maxLifetime = Math.random() * bubbleLifetime + 2000;

      // Random wobble parameters
      const wobbleSpeed = Math.random() * 2 + 1;
      const wobbleAmount = Math.random() * wobbleStrength + 0.5;

      // Random rise speed (bigger bubbles rise faster)
      const bubbleRiseSpeed = (size / maxSize) * riseSpeed + 0.5;

      // Create bubble color with transparency
      const baseHue = Math.random() < 0.7
        ? 187  // Turquoise (70% chance)
        : Math.random() < 0.5 ? 200 : 170; // Blue or teal variants

      const saturation = Math.floor(Math.random() * 20 + 80); // 80-100%
      const lightness = Math.floor(Math.random() * 20 + 70);  // 70-90%
      const opacity = Math.random() * 0.3 + 0.1;

      const bubbleColor = `hsla(${baseHue}, ${saturation}%, ${lightness}%, ${opacity})`;
      const borderColor = `hsla(${baseHue}, ${saturation}%, ${lightness}%, ${opacity + 0.1})`;
      const borderWidth = Math.max(1, Math.floor(size / 15));

      return {
        id,
        x,
        y,
        size,
        opacity: 0, // Start invisible and fade in
        lifetime: 0,
        maxLifetime,
        wobbleSpeed,
        wobbleAmount,
        riseSpeed: bubbleRiseSpeed,
        color: bubbleColor,
        borderColor,
        borderWidth
      };
    };

    const handleMouseMove = (e: MouseEvent) => {
      if (!containerRef.current) return;

      const rect = containerRef.current.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;

      // Calculate distance moved
      const dx = x - lastMousePosRef.current.x;
      const dy = y - lastMousePosRef.current.y;
      const distance = Math.sqrt(dx * dx + dy * dy);

      // Only create bubbles if mouse has moved a minimum distance
      if (distance > 3) {
        // Create more bubbles based on mouse movement speed
        const bubblesCount = Math.min(8, Math.max(3, Math.floor(distance / 10)));

        const newBubbles = Array.from({ length: bubblesCount }, () => {
          // Add random offset around cursor
          const offsetX = (Math.random() - 0.5) * 30;
          const offsetY = (Math.random() - 0.5) * 30;

          return createBubble(
            x + offsetX,
            y + offsetY
          );
        });

        // Add a few extra bubbles with larger spread for more coverage
        const extraBubbles = Array.from({ length: Math.floor(distance / 15) + 1 }, () => {
          const offsetX = (Math.random() - 0.5) * 60;
          const offsetY = (Math.random() - 0.5) * 60;

          return createBubble(
            x + offsetX,
            y + offsetY
          );
        });

        setBubbles(prev => {
          // Limit the number of bubbles
          const combinedBubbles = [...prev, ...newBubbles, ...extraBubbles];
          return combinedBubbles.slice(-maxBubbles);
        });

        lastMousePosRef.current = { x, y };
      }
    };

    const handleMouseEnter = (e: MouseEvent) => {
      if (!containerRef.current) return;

      const rect = containerRef.current.getBoundingClientRect();
      lastMousePosRef.current = {
        x: e.clientX - rect.left,
        y: e.clientY - rect.top
      };
    };

    window.addEventListener('mousemove', handleMouseMove);
    window.addEventListener('mouseenter', handleMouseEnter);

    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseenter', handleMouseEnter);
      cancelAnimationFrame(animationFrameRef.current);
    };
  }, [maxBubbles, minSize, maxSize, bubbleLifetime, riseSpeed, wobbleStrength]);

  // Animate bubbles
  useEffect(() => {
    if (!containerRef.current || bubbles.length === 0) return;

    const animate = () => {
      setBubbles(prev =>
        prev
          .map(bubble => {
            // Update lifetime
            const newLifetime = bubble.lifetime + 16; // Approximately 16ms per frame
            const lifeProgress = newLifetime / bubble.maxLifetime;

            // Fade in quickly, then slowly fade out near the end
            let newOpacity = bubble.opacity;
            if (lifeProgress < 0.1) {
              // Fade in during first 10% of life
              newOpacity = lifeProgress * 10;
            } else if (lifeProgress > 0.7) {
              // Fade out during last 30% of life
              newOpacity = Math.max(0, 1 - ((lifeProgress - 0.7) / 0.3));
            } else {
              // Full opacity during middle of life
              newOpacity = 1;
            }

            // Calculate wobble effect
            const wobbleX = Math.sin(newLifetime / 400 * bubble.wobbleSpeed) * bubble.wobbleAmount;

            // Move bubble upward (y decreases)
            const newY = bubble.y - bubble.riseSpeed;

            return {
              ...bubble,
              x: bubble.x + wobbleX,
              y: newY,
              lifetime: newLifetime,
              opacity: newOpacity
            };
          })
          .filter(bubble => bubble.lifetime < bubble.maxLifetime)
      );

      animationFrameRef.current = requestAnimationFrame(animate);
    };

    animationFrameRef.current = requestAnimationFrame(animate);

    return () => {
      cancelAnimationFrame(animationFrameRef.current);
    };
  }, [bubbles]);

  return (
    <div
      ref={containerRef}
      className="absolute inset-0 pointer-events-none z-10 overflow-hidden"
    >
      {bubbles.map(bubble => {
        const lifeProgress = bubble.lifetime / bubble.maxLifetime;

        // Scale effect - grow slightly at start, then shrink at end
        let scale = 1;
        if (lifeProgress < 0.2) {
          scale = 0.5 + lifeProgress * 2.5; // Grow from 0.5 to 1
        } else if (lifeProgress > 0.8) {
          scale = 1 - (lifeProgress - 0.8) * 0.5; // Shrink slightly at end
        }

        return (
          <div
            key={bubble.id}
            className="absolute rounded-full"
            style={{
              left: `${bubble.x}px`,
              top: `${bubble.y}px`,
              width: `${bubble.size}px`,
              height: `${bubble.size}px`,
              backgroundColor: bubble.color,
              border: `${bubble.borderWidth}px solid ${bubble.borderColor}`,
              opacity: bubble.opacity,
              transform: `scale(${scale})`,
              transition: 'transform 0.3s ease-out',
              boxShadow: `inset 0 0 ${bubble.size/4}px rgba(255, 255, 255, 0.3)`,
            }}
          />
        );
      })}
    </div>
  );
}
