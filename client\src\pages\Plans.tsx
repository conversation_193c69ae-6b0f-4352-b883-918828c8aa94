import { useEffect } from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { motion } from 'framer-motion';
import { Check, X, HelpCircle } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

// Define the feature types
interface PlanFeature {
  name: string;
  free: boolean;
  pro: boolean;
  ultimate: boolean;
  tooltip?: string;
}

// Define all features with their availability in each plan
const features: PlanFeature[] = [
  {
    name: "Requests per day",
    free: true,
    pro: true,
    ultimate: true,
    tooltip: "Free plan is limited to 10 requests per day. Pro and Ultimate plans have unlimited requests."
  },
  {
    name: "Scan any webpage",
    free: true,
    pro: true,
    ultimate: true,
    tooltip: "Analyze the content of any webpage you're browsing."
  },
  {
    name: "Basic content analysis",
    free: true,
    pro: true,
    ultimate: true,
    tooltip: "Get basic insights about the webpage content."
  },
  {
    name: "Advanced content analysis",
    free: false,
    pro: true,
    ultimate: true,
    tooltip: "Get deeper insights and more detailed analysis of webpage content."
  },
  {
    name: "Sarcastic commentary",
    free: true,
    pro: true,
    ultimate: true,
    tooltip: "Free plan has mild sarcasm. Pro and Ultimate plans have premium sarcastic commentary."
  },
  {
    name: "Smart search feature",
    free: false,
    pro: true,
    ultimate: true,
    tooltip: "Intelligently search through content with advanced filters and options."
  },
  {
    name: "Search across multiple tabs",
    free: false,
    pro: true,
    ultimate: true,
    tooltip: "Search for content across all your open browser tabs at once."
  },
  {
    name: "Export chat history",
    free: false,
    pro: true,
    ultimate: true,
    tooltip: "Save and export your conversation history with the AI."
  },
  {
    name: "Processing speed",
    free: true,
    pro: true,
    ultimate: true,
    tooltip: "Free plan has standard processing speed. Pro and Ultimate plans have blazing fast processing."
  },
  {
    name: "Custom roast intensity",
    free: false,
    pro: true,
    ultimate: true,
    tooltip: "Adjust how sarcastic and critical the AI should be."
  },
  {
    name: "Premium roast templates",
    free: false,
    pro: true,
    ultimate: true,
    tooltip: "Access to professionally crafted sarcastic templates."
  },
  {
    name: "Ad-free experience",
    free: false,
    pro: true,
    ultimate: true,
    tooltip: "No advertisements or promotional content."
  },
  {
    name: "Early access to new features",
    free: false,
    pro: false,
    ultimate: true,
    tooltip: "Be the first to try new features before they're released to other users."
  },
  {
    name: "Advanced usage analytics",
    free: false,
    pro: false,
    ultimate: true,
    tooltip: "Get insights about your usage patterns and browsing habits."
  },
  {
    name: "Priority 24/7 support",
    free: false,
    pro: false,
    ultimate: true,
    tooltip: "Get faster responses from our support team at any time."
  },
  {
    name: "Custom AI training options",
    free: false,
    pro: false,
    ultimate: true,
    tooltip: "Customize how the AI responds to better match your preferences."
  }
];

export default function Plans() {
  useEffect(() => {
    // Update document title
    document.title = "Pricing Plans - browzy - Supercharge Your Browser with AI";

    // Add meta description
    const metaDescription = document.createElement('meta');
    metaDescription.name = 'description';
    metaDescription.content = 'Explore browzy pricing plans. Choose from Free, Pro, and Ultimate plans to supercharge your browsing experience with AI.';
    document.head.appendChild(metaDescription);

    window.scrollTo(0, 0);

    return () => {
      document.head.removeChild(metaDescription);
    };
  }, []);

  return (
    <div className="min-h-screen dark:bg-black light:bg-white text-foreground overflow-hidden">
      <Header />

      <div className="max-w-6xl mx-auto px-6 sm:px-8 lg:px-12 pt-28 md:pt-32">
        <main className="py-8 md:py-12">
          <section className="py-12 md:py-16 relative">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="text-center mb-10"
            >
              <h1 className="section-heading font-heading text-5xl md:text-6xl font-bold inline-block mx-auto tracking-tight mb-6">
                <span className="text-primary neon-text">Pricing</span> Plans Comparison
              </h1>
              <p className="text-muted-foreground mt-4 max-w-3xl mx-auto text-xl font-body">
                All plans include webpage scanning. Free plan limited to 10 requests per day. Upgrade to Pro or Ultimate for unlimited requests and premium features.
              </p>
            </motion.div>

            {/* Pricing Cards */}
            <div className="grid md:grid-cols-3 gap-6 md:gap-8 max-w-[98%] lg:max-w-[100%] xl:max-w-[102%] mx-auto mb-16">
              {/* Free Plan */}
              <div className="modern-card h-full flex flex-col transition-all duration-300 overflow-hidden group relative">
                <div className="p-5 md:p-7 flex flex-col h-full">
                  <div className="mb-6">
                    <h3 className="font-heading text-2xl font-semibold mb-2 group-hover:text-secondary transition-colors duration-300 tracking-tight">
                      Free
                    </h3>
                    <p className="text-muted-foreground mb-5 font-body">
                      Basic features for casual browsing with a side of sass.
                    </p>
                    <div className="flex items-baseline mb-5">
                      <span className="text-4xl font-bold font-heading">$0</span>
                      <span className="ml-2 text-muted-foreground">forever</span>
                    </div>

                    <button
                      onClick={() => {
                        console.log('Free plan button clicked');
                      }}
                      className="w-full py-2.5 px-4 rounded-md font-medium text-center transition-colors duration-200 border border-primary/50 text-primary hover:bg-primary/10"
                    >
                      Get Started
                    </button>
                  </div>

                  <div className="border-t border-border pt-5 flex-grow">
                    <div className="text-sm text-muted-foreground mb-4">
                      <strong>10 requests per day</strong> - Perfect for casual users
                    </div>
                  </div>
                </div>
              </div>

              {/* Pro Plan */}
              <div className="modern-card h-full flex flex-col transition-all duration-300 overflow-hidden group relative border-primary/50 shadow-lg shadow-primary/10">
                <div className="absolute top-0 right-0">
                  <div className="bg-primary text-white text-xs font-bold px-4 py-1.5 transform rotate-0 origin-top-right">
                    POPULAR
                  </div>
                </div>

                <div className="p-5 md:p-7 flex flex-col h-full">
                  <div className="mb-6">
                    <h3 className="font-heading text-2xl font-semibold mb-2 group-hover:text-primary transition-colors duration-300 tracking-tight">
                      Pro
                    </h3>
                    <p className="text-muted-foreground mb-5 font-body">
                      Full access to all features with maximum judgment.
                    </p>
                    <div className="flex items-baseline mb-5">
                      <span className="text-4xl font-bold font-heading">$20</span>
                      <span className="ml-2 text-muted-foreground">per month</span>
                    </div>

                    <button
                      onClick={async () => {
                        try {
                          // Get the price ID for the Pro plan
                          const priceId = 'price_1RLGJvSABghPrT7Lx1w689k3';

                          const controller = new AbortController();
                          const timeoutId = setTimeout(() => controller.abort(), 3000);

                          // Create a checkout session
                          const response = await fetch('http://localhost:5000/api/payment/create-checkout-session-public', {
                            method: 'POST',
                            headers: {
                              'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                              email: '<EMAIL>',
                              planId: 'pro',
                              priceId
                            }),
                            signal: controller.signal
                          });

                          clearTimeout(timeoutId);

                          if (!response.ok) {
                            return;
                          }

                          const responseData = await response.json();

                          if (responseData.url) {
                            window.location.href = responseData.url;
                          }
                        } catch (error) {
                          // Silently handle errors
                        }
                      }}
                      className="w-full py-2.5 px-4 rounded-md font-medium text-center transition-colors duration-200 bg-primary text-white hover:bg-primary/90"
                    >
                      Upgrade Now
                    </button>
                  </div>

                  <div className="border-t border-border pt-5 flex-grow">
                    <div className="text-sm text-muted-foreground mb-4">
                      <strong>Unlimited requests</strong> - For power users who need more
                    </div>
                  </div>
                </div>
              </div>

              {/* Ultimate Plan */}
              <div className="modern-card h-full flex flex-col transition-all duration-300 overflow-hidden group relative">
                <div className="absolute top-0 right-0">
                  <div className="bg-accent text-white text-xs font-bold px-4 py-1.5 transform rotate-0 origin-top-right">
                    PREMIUM
                  </div>
                </div>

                <div className="p-5 md:p-7 flex flex-col h-full">
                  <div className="mb-6">
                    <h3 className="font-heading text-2xl font-semibold mb-2 group-hover:text-accent transition-colors duration-300 tracking-tight">
                      Ultimate
                    </h3>
                    <p className="text-muted-foreground mb-5 font-body">
                      The most comprehensive package for serious critics.
                    </p>
                    <div className="flex items-baseline mb-5">
                      <span className="text-4xl font-bold font-heading">$200</span>
                      <span className="ml-2 text-muted-foreground">per year</span>
                    </div>

                    <button
                      onClick={async () => {
                        try {
                          // Get the price ID for the Ultimate plan
                          const priceId = 'price_1RLGO3SABghPrT7Lw0jRLe9C';

                          const controller = new AbortController();
                          const timeoutId = setTimeout(() => controller.abort(), 3000);

                          // Create a checkout session
                          const response = await fetch('http://localhost:5000/api/payment/create-checkout-session-public', {
                            method: 'POST',
                            headers: {
                              'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                              email: '<EMAIL>',
                              planId: 'ultimate',
                              priceId
                            }),
                            signal: controller.signal
                          });

                          clearTimeout(timeoutId);

                          if (!response.ok) {
                            return;
                          }

                          const responseData = await response.json();

                          if (responseData.url) {
                            window.location.href = responseData.url;
                          }
                        } catch (error) {
                          // Silently handle errors
                        }
                      }}
                      className="w-full py-2.5 px-4 rounded-md font-medium text-center transition-colors duration-200 border border-primary/50 text-primary hover:bg-primary/10"
                    >
                      Go Ultimate
                    </button>
                  </div>

                  <div className="border-t border-border pt-5 flex-grow">
                    <div className="text-sm text-muted-foreground mb-4">
                      <strong>Everything in Pro</strong> - Plus exclusive premium features
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Features Comparison Table */}
            <TooltipProvider>
              <div className="overflow-x-auto">
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="border-b border-border">
                      <th className="text-left py-4 px-4 font-heading text-lg">Feature</th>
                      <th className="text-center py-4 px-4 font-heading text-lg">Free</th>
                      <th className="text-center py-4 px-4 font-heading text-lg">Pro</th>
                      <th className="text-center py-4 px-4 font-heading text-lg">Ultimate</th>
                    </tr>
                  </thead>
                  <tbody>
                    {features.map((feature, index) => (
                      <tr key={index} className={index % 2 === 0 ? 'bg-muted/20' : ''}>
                        <td className="py-3 px-4 flex items-center">
                          {feature.name}
                          {feature.tooltip && (
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <HelpCircle className="h-4 w-4 ml-2 text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent>
                                <p className="max-w-xs">{feature.tooltip}</p>
                              </TooltipContent>
                            </Tooltip>
                          )}
                        </td>
                        <td className="py-3 px-4 text-center">
                          {feature.name === "Requests per day" ? (
                            <span className="text-sm font-medium">10</span>
                          ) : feature.name === "Processing speed" ? (
                            <span className="text-sm font-medium">Standard</span>
                          ) : feature.name === "Sarcastic commentary" ? (
                            <span className="text-sm font-medium">Mild</span>
                          ) : feature.free ? (
                            <Check className="h-5 w-5 text-primary mx-auto" />
                          ) : (
                            <X className="h-5 w-5 text-muted-foreground mx-auto" />
                          )}
                        </td>
                        <td className="py-3 px-4 text-center">
                          {feature.name === "Requests per day" ? (
                            <span className="text-sm font-medium">Unlimited</span>
                          ) : feature.name === "Processing speed" ? (
                            <span className="text-sm font-medium">Fast</span>
                          ) : feature.name === "Sarcastic commentary" ? (
                            <span className="text-sm font-medium">Premium</span>
                          ) : feature.pro ? (
                            <Check className="h-5 w-5 text-primary mx-auto" />
                          ) : (
                            <X className="h-5 w-5 text-muted-foreground mx-auto" />
                          )}
                        </td>
                        <td className="py-3 px-4 text-center">
                          {feature.name === "Requests per day" ? (
                            <span className="text-sm font-medium">Unlimited</span>
                          ) : feature.name === "Processing speed" ? (
                            <span className="text-sm font-medium">Fastest</span>
                          ) : feature.name === "Sarcastic commentary" ? (
                            <span className="text-sm font-medium">Premium+</span>
                          ) : feature.ultimate ? (
                            <Check className="h-5 w-5 text-primary mx-auto" />
                          ) : (
                            <X className="h-5 w-5 text-muted-foreground mx-auto" />
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </TooltipProvider>

            <div className="mt-12 text-center">
              <p className="text-muted-foreground text-sm">
                All plans include a 14-day money-back guarantee. No questions asked, except maybe "Why didn't you appreciate our sarcasm?"
              </p>
            </div>

            <div className="mt-16 text-center">
              <h2 className="font-heading text-3xl font-bold mb-6">Ready to Get Started?</h2>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button
                  onClick={async () => {
                    try {
                      // Get the price ID for the Pro plan (default)
                      const priceId = 'price_1RLGJvSABghPrT7Lx1w689k3';

                      const controller = new AbortController();
                      const timeoutId = setTimeout(() => controller.abort(), 3000);

                      // Create a checkout session
                      const response = await fetch('http://localhost:5000/api/payment/create-checkout-session-public', {
                        method: 'POST',
                        headers: {
                          'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                          email: '<EMAIL>',
                          planId: 'pro',
                          priceId
                        }),
                        signal: controller.signal
                      });

                      clearTimeout(timeoutId);

                      if (!response.ok) {
                        return;
                      }

                      const responseData = await response.json();

                      if (responseData.url) {
                        window.location.href = responseData.url;
                      }
                    } catch (error) {
                      // Silently handle errors
                    }
                  }}
                  className="px-8 py-3 bg-primary text-white rounded-md hover:bg-primary/90 transition-colors duration-200 font-medium shadow-sm"
                >
                  Install Now
                </button>
                <a
                  href="#contact"
                  className="px-8 py-3 border border-primary/50 text-primary rounded-md hover:bg-primary/10 transition-colors duration-200 font-medium"
                >
                  Contact Sales
                </a>
              </div>
            </div>
          </section>
        </main>
        <Footer />
      </div>
    </div>
  );
}
