import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { PostgrestError } from '@supabase/postgrest-js';
import { initializeFreeSubscription } from './services/subscriptionService';

// Initialize the Supabase client with server-side credentials from environment variables
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL || '';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY || '';
const supabaseAnonKey = process.env.SUPABASE_ANON_KEY || '';

console.log('Supabase URL:', supabaseUrl);
console.log('Supabase Service Key:', supabaseServiceKey ? 'Set correctly' : 'Missing');
console.log('Supabase Anon Key:', supabaseAnonKey ? 'Set correctly' : 'Missing');

// Create a Supabase client with the service key for admin access
export const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Create a Supabase client with the anon key for public access
export const supabasePublic = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true
  }
});

// Types for authentication responses
export interface AuthResponse {
  user: any | null;
  session: any | null;
  error: Error | null;
}

/**
 * Verify a user's JWT token from Supabase
 * @param token JWT token from client
 * @returns User data if token is valid, null otherwise
 */
export async function verifyToken(token: string) {
  try {
    const { data, error } = await supabaseAdmin.auth.getUser(token);

    if (error) {
      console.error('Error verifying token:', error);
      return null;
    }

    return data.user;
  } catch (error) {
    console.error('Exception during token verification:', error);
    return null;
  }
}

/**
 * Sign in a user with email and password
 * @param email User's email
 * @param password User's password
 * @returns Auth response with user and session data
 */
export async function signInWithEmail(email: string, password: string): Promise<AuthResponse> {
  try {
    console.log('Server: Signing in with email:', email);

    const { data, error } = await supabasePublic.auth.signInWithPassword({
      email,
      password,
      options: {
        // Set remember me to true for 30-day persistence
      }
    });

    if (error) {
      console.error('Server: Sign in error:', error);
      return { user: null, session: null, error };
    }

    console.log('Server: Sign in successful');
    return {
      user: data.user,
      session: data.session,
      error: null
    };
  } catch (error) {
    console.error('Server: Exception during sign in:', error);
    return {
      user: null,
      session: null,
      error: error as Error
    };
  }
}

/**
 * Sign up a new user with email and password
 * @param email User's email
 * @param password User's password
 * @returns Auth response with user and session data
 */
export async function signUpWithEmail(email: string, password: string): Promise<AuthResponse> {
  try {
    console.log('Server: Signing up with email:', email);

    const { data, error } = await supabasePublic.auth.signUp({
      email,
      password,
      options: {
        emailRedirectTo: `${process.env.CLIENT_URL || 'http://localhost:5173'}/signin`,
        data: {
          signup_date: new Date().toISOString(),
        }
      }
    });

    if (error) {
      console.error('Server: Sign up error:', error);
      return { user: null, session: null, error };
    }

    console.log('Server: Sign up successful');

    // Create a profile for the new user
    if (data.user) {
      await createUserProfile(data.user.id, {
        email: data.user.email,
        created_at: new Date().toISOString(),
        preferences: {
          theme: 'light',
          notifications: true
        }
      });

      // Initialize a free subscription for the new user
      await initializeFreeSubscription(data.user.id);
    }

    return {
      user: data.user,
      session: data.session,
      error: null
    };
  } catch (error) {
    console.error('Server: Exception during sign up:', error);
    return {
      user: null,
      session: null,
      error: error as Error
    };
  }
}

/**
 * Sign in a user with Google OAuth
 * @returns Auth response with redirect URL
 */
export async function signInWithGoogle() {
  try {
    console.log('Server: Initiating Google sign in');

    const { data, error } = await supabasePublic.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: `${process.env.CLIENT_URL || 'http://localhost:5173'}`,
        queryParams: {
          prompt: 'select_account',
        },
        // Removed invalid 'remember' property
      },
    });

    if (error) {
      console.error('Server: Google sign in error:', error);
      return { url: null, error };
    }

    console.log('Server: Google sign in initiated, redirect URL:', data?.url);
    return { url: data?.url, error: null };
  } catch (error) {
    console.error('Server: Exception during Google sign in:', error);
    return { url: null, error: error as Error };
  }
}

/**
 * Sign out a user
 * @param token User's JWT token
 * @returns Success status
 */
export async function signOut(token: string) {
  try {
    console.log('Server: Signing out user');

    // First verify the token
    const user = await verifyToken(token);
    if (!user) {
      console.error('Server: Invalid token during sign out');
      return { success: false, error: new Error('Invalid token') };
    }

    // Sign out the user
    const { error } = await supabaseAdmin.auth.admin.signOut(token);

    if (error) {
      console.error('Server: Sign out error:', error);
      return { success: false, error };
    }

    console.log('Server: Sign out successful');
    return { success: true, error: null };
  } catch (error) {
    console.error('Server: Exception during sign out:', error);
    return { success: false, error: error as Error };
  }
}

/**
 * Get user data by ID
 * @param userId User ID
 * @returns User data if found, null otherwise
 */
export async function getUserById(userId: string) {
  try {
    const { data, error } = await supabaseAdmin
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single();

    if (error) {
      console.error('Error getting user data:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Exception during get user:', error);
    return null;
  }
}

/**
 * Create a user profile
 * @param userId User ID
 * @param profileData Profile data
 * @returns Created profile data if successful, null otherwise
 */
export async function createUserProfile(userId: string, profileData: any) {
  try {
    const { data, error } = await supabaseAdmin
      .from('profiles')
      .insert([
        {
          id: userId,
          ...profileData
        }
      ])
      .select()
      .single();

    if (error) {
      console.error('Error creating user profile:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Exception during create profile:', error);
    return null;
  }
}

/**
 * Update user preferences
 * @param userId User ID
 * @param preferences User preferences object
 * @returns Updated user data if successful, null otherwise
 */
export async function updateUserPreferences(userId: string, preferences: any) {
  try {
    const { data, error } = await supabaseAdmin
      .from('profiles')
      .update({ preferences })
      .eq('id', userId)
      .select()
      .single();

    if (error) {
      console.error('Error updating user preferences:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Exception during update preferences:', error);
    return null;
  }
}

/**
 * Refresh a user's session
 * @param refreshToken User's refresh token
 * @returns New session if successful, null otherwise
 */
export async function refreshSession(refreshToken: string) {
  try {
    const { data, error } = await supabasePublic.auth.refreshSession({
      refresh_token: refreshToken
    });

    if (error) {
      console.error('Error refreshing session:', error);
      return { session: null, error };
    }

    return { session: data.session, error: null };
  } catch (error) {
    console.error('Exception during session refresh:', error);
    return { session: null, error: error as Error };
  }
}
