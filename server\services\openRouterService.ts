/**
 * OpenRouter Service
 * Handles communication with the OpenRouter API
 */

import axios from 'axios';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// OpenRouter API key from environment variables
const OPENROUTER_API_KEY = process.env.OPENROUTER_API_KEY;

// Check if OpenRouter API key is configured
if (!OPENROUTER_API_KEY) {
  console.warn('WARNING: OPENROUTER_API_KEY is not set in environment variables');
}

/**
 * Make a request to the OpenRouter API
 * @param data Request data for OpenRouter
 * @returns Response from OpenRouter
 */
export async function makeOpenRouterRequest(data: any) {
  try {
    // Validate that we have an API key
    if (!OPENROUTER_API_KEY) {
      throw new Error('OpenRouter API key is not configured on the server');
    }

    // Ensure the model is set to a supported GPT-3.5 variant
    // This endpoint only supports these specific models
    if (data.model !== 'openai/gpt-3.5-turbo' && data.model !== 'openai/gpt-3.5-turbo-16k') {
      console.log(`Requested model ${data.model} not supported, defaulting to gpt-3.5-turbo-16k`);
      data.model = 'openai/gpt-3.5-turbo-16k';
    }

    // Make the request to OpenRouter
    const response = await axios({
      method: 'POST',
      url: 'https://openrouter.ai/api/v1/chat/completions',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'HTTP-Referer': 'https://browzyai.app',
        'X-Title': 'BrowzyAI Backend'
      },
      data
    });

    return {
      success: true,
      data: response.data
    };
  } catch (error: any) {
    // Log detailed error information
    console.error('OpenRouter API error:', {
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      url: 'https://openrouter.ai/api/v1/chat/completions'
    });

    // Construct a more detailed error message
    let errorMessage = 'Error calling OpenRouter API';

    if (error.response) {
      // Axios error with response
      errorMessage = `OpenRouter API error (${error.response.status}): ${
        error.response.data?.error?.message ||
        error.response.data?.error ||
        error.response.statusText ||
        'Unknown error'
      }`;
    } else if (error.request) {
      // Request was made but no response received
      errorMessage = 'No response received from OpenRouter API. Please check your network connection.';
    } else {
      // Error in setting up the request
      errorMessage = `Error setting up OpenRouter request: ${error.message}`;
    }

    return {
      success: false,
      error: errorMessage,
      details: error.response?.data || null
    };
  }
}

/**
 * Track usage for a user
 * @param userId User ID
 * @param tokens Number of tokens used
 */
export async function trackOpenRouterUsage(userId: string, tokens: number) {
  try {
    // TODO: Implement usage tracking in database
    console.log(`Tracked ${tokens} tokens for user ${userId}`);
    return true;
  } catch (error: any) {
    console.error('Error tracking OpenRouter usage:', error);
    return false;
  }
}
