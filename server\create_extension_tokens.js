// <PERSON>ript to create the extension_tokens table directly
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const axios = require('axios');
const fs = require('fs');
const path = require('path');

// Initialize Supabase client with service role key
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY;

console.log('Supabase URL:', supabaseUrl);
console.log('Supabase Service Key:', supabaseServiceKey ? 'Set correctly' : 'Missing');

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase URL or Service Key. Please check your environment variables.');
  process.exit(1);
}

// Create Supabase client
const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Function to execute SQL using Supabase REST API
async function executeSql(sql) {
  try {
    const response = await axios({
      method: 'POST',
      url: `${supabaseUrl}/rest/v1/`,
      headers: {
        'Content-Type': 'application/json',
        'apikey': supabaseServiceKey,
        'Authorization': `Bearer ${supabaseServiceKey}`,
        'Prefer': 'resolution=merge-duplicates'
      },
      data: {
        query: sql
      }
    });

    return { data: response.data, error: null };
  } catch (error) {
    console.error('SQL execution error:', error.response?.data || error.message);
    return { data: null, error: error.response?.data || error.message };
  }
}

async function createExtensionTokensTable() {
  try {
    console.log('Creating extension_tokens table...');

    // Create a test table to check if we can execute SQL
    console.log('Testing SQL execution...');

    // First, let's check if the table already exists
    const { data: tableData, error: tableError } = await supabaseAdmin
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .eq('table_name', 'extension_tokens');

    if (tableError) {
      console.error('Error checking if table exists:', tableError);
    } else {
      console.log('Table check result:', tableData);

      if (tableData && tableData.length > 0) {
        console.log('Table extension_tokens already exists!');
        return;
      }
    }

    // Create the table using Supabase REST API
    console.log('Creating extension_tokens table...');

    // Create the table
    await supabaseAdmin.rpc('create_extension_tokens_table', {});

    console.log('Successfully created extension_tokens table');

  } catch (error) {
    console.error('Error creating extension_tokens table:', error);
    process.exit(1);
  }
}

// Create a stored procedure in Supabase to create the extension_tokens table
async function createStoredProcedure() {
  try {
    console.log('Creating stored procedure...');

    const sql = `
      CREATE OR REPLACE FUNCTION create_extension_tokens_table()
      RETURNS void AS $$
      BEGIN
        -- Create extension_tokens table
        CREATE TABLE IF NOT EXISTS extension_tokens (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
          token VARCHAR(20) NOT NULL UNIQUE,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          last_used TIMESTAMP WITH TIME ZONE,
          CONSTRAINT token_length CHECK (char_length(token) = 9)
        );

        -- Create index on user_id for faster lookups
        CREATE INDEX IF NOT EXISTS idx_extension_tokens_user_id ON extension_tokens(user_id);

        -- Create index on token for faster validation
        CREATE INDEX IF NOT EXISTS idx_extension_tokens_token ON extension_tokens(token);

        -- Enable RLS on extension_tokens
        ALTER TABLE extension_tokens ENABLE ROW LEVEL SECURITY;

        -- Create policy to allow users to see only their own tokens
        CREATE POLICY IF NOT EXISTS select_own_tokens ON extension_tokens
          FOR SELECT USING (auth.uid() = user_id);

        -- Create policy to allow users to insert their own tokens
        CREATE POLICY IF NOT EXISTS insert_own_tokens ON extension_tokens
          FOR INSERT WITH CHECK (auth.uid() = user_id);

        -- Create policy to allow users to update their own tokens
        CREATE POLICY IF NOT EXISTS update_own_tokens ON extension_tokens
          FOR UPDATE USING (auth.uid() = user_id);

        -- Create policy to allow users to delete their own tokens
        CREATE POLICY IF NOT EXISTS delete_own_tokens ON extension_tokens
          FOR DELETE USING (auth.uid() = user_id);
      END;
      $$ LANGUAGE plpgsql;
    `;

    // Execute the SQL to create the stored procedure
    const { error } = await executeSql(sql);

    if (error) {
      console.error('Error creating stored procedure:', error);
      throw error;
    }

    console.log('Successfully created stored procedure');
    return true;
  } catch (error) {
    console.error('Error creating stored procedure:', error);
    return false;
  }
}

// Run the functions
async function main() {
  try {
    // First create the stored procedure
    const procedureCreated = await createStoredProcedure();

    if (procedureCreated) {
      // Then create the table using the stored procedure
      await createExtensionTokensTable();
    } else {
      console.error('Failed to create stored procedure, cannot continue');
      process.exit(1);
    }

    console.log('Script completed successfully');
    process.exit(0);
  } catch (error) {
    console.error('Script failed:', error);
    process.exit(1);
  }
}

main();
