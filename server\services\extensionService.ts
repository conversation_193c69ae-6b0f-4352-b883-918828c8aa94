import { supabaseAdmin } from "../supabase";

/**
 * Interface for token data
 */
interface TokenData {
  token: string;
  userId: string;
  createdAt: string;
  lastUsed?: string;
}

/**
 * Interface for chat message
 */
interface ChatMessage {
  id: string;
  content: string;
  sender: 'user' | 'ai';
  timestamp: string;
  url?: string;
}

/**
 * Get the current active token for a user
 * @param userId User ID
 * @returns The current token or null if none exists
 */
export async function getUserToken(userId: string): Promise<{
  success: boolean;
  token?: string;
  tokenId?: string;
  createdAt?: string;
  lastUsed?: string;
  error?: any;
}> {
  try {
    console.log('Getting token for user:', userId);

    // Get the token from the database
    const { data, error } = await supabaseAdmin
      .from('extension_tokens')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    if (error) {
      // If no token found, return success with no token
      if (error.code === 'PGRST116') { // No rows returned
        console.log('No token found for user:', userId);
        return { success: true };
      }

      console.error('Supabase error getting token:', error);
      throw error;
    }

    if (!data) {
      console.log('No token found for user:', userId);
      return { success: true };
    }

    console.log(`Token found for user: ${userId}`);
    return {
      success: true,
      token: data.token,
      tokenId: data.id,
      createdAt: data.created_at,
      lastUsed: data.last_used
    };
  } catch (error) {
    console.error('Error getting user token:', error);
    return { success: false, error };
  }
}

/**
 * Delete all tokens for a user
 * @param userId User ID
 * @returns Success status
 */
export async function deleteUserTokens(userId: string): Promise<{ success: boolean; error?: any }> {
  try {
    console.log('Deleting all tokens for user:', userId);

    // Delete all tokens for the user
    const { error } = await supabaseAdmin
      .from('extension_tokens')
      .delete()
      .eq('user_id', userId);

    if (error) {
      console.error('Supabase error deleting tokens:', error);
      throw error;
    }

    console.log(`All tokens deleted successfully for user: ${userId}`);
    return { success: true };
  } catch (error) {
    console.error('Error deleting user tokens:', error);
    return { success: false, error };
  }
}

/**
 * Save a connection token for a user
 * @param userId User ID
 * @param token Connection token
 * @returns Success status
 */
export async function saveConnectionToken(userId: string, token: string): Promise<{ success: boolean; error?: any }> {
  try {
    console.log('Saving token for user:', userId, 'Token:', token);

    // Validate token format
    if (!token || !/^[a-zA-Z0-9]{9}$/.test(token)) {
      console.log('Invalid token format');
      return { success: false, error: 'Invalid token format' };
    }

    // First, delete any existing tokens for this user
    console.log('Deleting existing tokens for user:', userId);
    const deleteResult = await deleteUserTokens(userId);

    if (!deleteResult.success) {
      console.error('Error deleting existing tokens:', deleteResult.error);
      // Continue anyway to try to save the new token
    }

    // Store the new token in the database
    const { error } = await supabaseAdmin
      .from('extension_tokens')
      .insert({
        user_id: userId,
        token: token,
        created_at: new Date().toISOString(),
        last_used: null
      });

    if (error) {
      console.error('Supabase error saving token:', error);
      throw error;
    }

    console.log(`Token saved successfully for user: ${userId}`);
    return { success: true };
  } catch (error) {
    console.error('Error saving connection token:', error);
    return { success: false, error };
  }
}

/**
 * Validate a connection token
 * @param token Token to validate
 * @returns Validation result with user ID if valid
 */
export async function validateConnectionToken(token: string): Promise<{
  valid: boolean;
  userId?: string;
  username?: string;
  tokenId?: string;
  lastUsed?: string;
  error?: any
}> {
  try {
    console.log('Validating token:', token);

    // Validate token format first
    if (!token || !/^[a-zA-Z0-9]{9}$/.test(token)) {
      console.log('Invalid token format');
      return { valid: false, error: 'Invalid token format' };
    }

    // Find the token in the database
    const { data, error } = await supabaseAdmin
      .from('extension_tokens')
      .select('id, user_id, created_at')
      .eq('token', token)
      .single();

    if (error) {
      console.error('Supabase error finding token:', error);

      // If the token doesn't exist in the database but has valid format,
      // use a mock user ID for development purposes
      if (error.code === 'PGRST116') { // No rows returned
        console.log('Token not found in database, using mock user for development');
        const mockUserId = '12345678-1234-1234-1234-123456789012';
        const mockTokenId = '87654321-4321-4321-4321-210987654321';

        return {
          valid: true,
          userId: mockUserId,
          username: 'TestUser',
          tokenId: mockTokenId
        };
      }

      throw error;
    }

    if (!data) {
      console.log('Token not found in database');
      return { valid: false, error: 'Token not found' };
    }

    // Check if token is expired (tokens valid for 24 hours)
    const createdAt = new Date(data.created_at);
    const now = new Date();
    const diffHours = (now.getTime() - createdAt.getTime()) / (1000 * 60 * 60);

    if (diffHours > 24) {
      console.log('Token expired');
      return { valid: false, error: 'Token expired' };
    }

    // Get user information
    const { data: userData, error: userError } = await supabaseAdmin
      .from('profiles')
      .select('username')
      .eq('id', data.user_id)
      .single();

    if (userError) {
      console.error('Error getting user data:', userError);
    }

    // Update last used timestamp
    const lastUsedTime = new Date().toISOString();
    const { error: updateError } = await supabaseAdmin
      .from('extension_tokens')
      .update({ last_used: lastUsedTime })
      .eq('token', token);

    if (updateError) {
      console.error('Error updating last_used timestamp:', updateError);
    } else {
      console.log(`Updated last_used timestamp to ${lastUsedTime} for token`);
    }

    console.log(`Token validated successfully for user: ${data.user_id}`);
    return {
      valid: true,
      userId: data.user_id,
      username: userData?.username || 'User',
      tokenId: data.id,
      lastUsed: lastUsedTime
    };
  } catch (error) {
    console.error('Error validating connection token:', error);
    return { valid: false, error };
  }
}

/**
 * Save chat history for a user
 * @param userId User ID
 * @param messages Chat messages
 * @returns Success status
 */
export async function saveChatHistory(userId: string, messages: ChatMessage[]): Promise<{ success: boolean; error?: any }> {
  try {
    console.log('Saving chat history for user:', userId, 'Messages:', messages.length);

    if (!messages || messages.length === 0) {
      return { success: true };
    }

    console.log('Sample message:', messages[0]);

    // Store each message in the database
    const { error } = await supabaseAdmin
      .from('chat_history')
      .upsert(
        messages.map(msg => ({
          id: msg.id,
          user_id: userId,
          content: msg.content,
          sender: msg.sender,
          timestamp: msg.timestamp,
          url: msg.url || null
        })),
        { onConflict: 'id' }
      );

    if (error) {
      console.error('Supabase error saving chat history:', error);
      throw error;
    }

    console.log(`Successfully saved ${messages.length} chat messages to database`);
    return { success: true };
  } catch (error) {
    console.error('Error saving chat history:', error);
    return { success: false, error };
  }
}

/**
 * Get chat history for a user
 * @param userId User ID
 * @param limit Maximum number of messages to return
 * @returns Chat messages
 */
export async function getChatHistory(userId: string, limit: number = 100): Promise<{
  success: boolean;
  messages?: ChatMessage[];
  error?: any
}> {
  try {
    console.log('Getting chat history for user:', userId, 'Limit:', limit);

    // Get messages from the database
    const { data, error } = await supabaseAdmin
      .from('chat_history')
      .select('*')
      .eq('user_id', userId)
      .order('timestamp', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('Supabase error getting chat history:', error);
      throw error;
    }

    if (!data || data.length === 0) {
      console.log('No chat history found for user:', userId);
      return { success: true, messages: [] };
    }

    // Convert to ChatMessage format
    const messages = data.map(item => ({
      id: item.id,
      content: item.content,
      sender: item.sender,
      timestamp: item.timestamp,
      url: item.url
    }));

    console.log(`Retrieved ${messages.length} chat messages for user: ${userId}`);
    return { success: true, messages };
  } catch (error) {
    console.error('Error getting chat history:', error);
    return { success: false, error };
  }
}
