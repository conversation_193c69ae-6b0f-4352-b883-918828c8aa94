/**
 * <PERSON><PERSON><PERSON> to check for expired subscriptions and downgrade them to the free plan
 * 
 * This script can be run as a scheduled task (e.g., using cron) to periodically
 * check for expired subscriptions and downgrade them to the free plan.
 * 
 * Example cron job (run daily at midnight):
 * 0 0 * * * node /path/to/server/scripts/check-expired-subscriptions.js
 */

// Load environment variables
require('dotenv').config({ path: '../.env' });

// Import the subscription service
const { checkExpiredSubscriptions } = require('../services/subscriptionService');

async function main() {
  try {
    console.log('Checking for expired subscriptions...');
    
    // Check for expired subscriptions and downgrade them to the free plan
    const count = await checkExpiredSubscriptions();
    
    console.log(`Downgraded ${count} expired subscriptions to the free plan.`);
    
    process.exit(0);
  } catch (error) {
    console.error('Error checking expired subscriptions:', error);
    process.exit(1);
  }
}

// Run the script
main();
