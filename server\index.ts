import express, { type Request, Response, NextFunction } from "express";
import { registerRoutes } from "./routes";
import { setupVite, serveStatic, log } from "./vite";
import dotenv from "dotenv";
import cookieParser from "cookie-parser";
import helmet from "helmet";
import cors from "cors";

// Load environment variables from .env file
import path from 'path';
dotenv.config({ path: path.resolve(__dirname, '.env') });

const app = express();

// Security middleware
app.use(helmet({
  contentSecurityPolicy: process.env.NODE_ENV === 'production' ? undefined : false,
}));

// Configure CORS to allow credentials and extension requests
app.use(cors({
  origin: [
    'http://localhost:5173',
    'http://localhost:5174',
    'http://127.0.0.1:5173',
    'http://127.0.0.1:5174',
    'chrome-extension://nfjipaniijjigbomgmnciigeikblnfmk',
    'https://api.trueailabs.com' // Production API domain
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

// Add a middleware to handle CORS preflight for extension requests
app.options('*', (req, res) => {
  // Set CORS headers for preflight requests
  res.header('Access-Control-Allow-Origin', req.headers.origin || '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
  res.header('Access-Control-Allow-Credentials', 'true');
  res.status(200).send();
});

// Add a middleware to ensure CORS headers are set for all responses
app.use((req, res, next) => {
  // Get the origin from the request headers
  const origin = req.headers.origin;

  // If the origin is from your Chrome extension or production API, allow it
  if (
    origin === 'chrome-extension://nfjipaniijjigbomgmnciigeikblnfmk' ||
    origin === 'https://api.trueailabs.com'
  ) {
    res.header('Access-Control-Allow-Origin', origin);
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
    res.header('Access-Control-Allow-Credentials', 'true');
  }

  next();
});

// Raw body parser for Stripe webhooks
app.use('/api/payment/webhook', express.raw({ type: 'application/json' }));

// Body parsing middleware for other routes
app.use((req, res, next) => {
  if (req.originalUrl === '/api/payment/webhook') {
    next();
  } else {
    express.json()(req, res, next);
  }
});
app.use(express.urlencoded({ extended: false }));

// Cookie parser with a secret for signed cookies
app.use(cookieParser(process.env.COOKIE_SECRET || 'sarcasticsoul-secret-key'));

// Add session check middleware
app.use((req, res, next) => {
  // Log all cookies for debugging
  console.log('Cookies received:', req.cookies);

  // Check for auth token in cookies
  const authToken = req.cookies['supabase-auth-token'];
  const refreshToken = req.cookies['supabase-auth-refresh'];

  if (authToken) {
    console.log('Auth token found in cookies');
    // Add token to Authorization header if not already present
    if (!req.headers.authorization) {
      req.headers.authorization = `Bearer ${authToken}`;
    }
  } else if (refreshToken) {
    console.log('Refresh token found, but no auth token');
  }

  next();
});

app.use((req, res, next) => {
  const start = Date.now();
  const path = req.path;
  let capturedJsonResponse: Record<string, any> | undefined = undefined;

  const originalResJson = res.json;
  res.json = function (bodyJson, ...args) {
    capturedJsonResponse = bodyJson;
    return originalResJson.apply(res, [bodyJson, ...args]);
  };

  res.on("finish", () => {
    const duration = Date.now() - start;
    if (path.startsWith("/api")) {
      let logLine = `${req.method} ${path} ${res.statusCode} in ${duration}ms`;
      if (capturedJsonResponse) {
        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;
      }

      if (logLine.length > 80) {
        logLine = logLine.slice(0, 79) + "…";
      }

      log(logLine);
    }
  });

  next();
});

(async () => {
  const server = await registerRoutes(app);

  app.use((err: any, _req: Request, res: Response, _next: NextFunction) => {
    const status = err.status || err.statusCode || 500;
    const message = err.message || "Internal Server Error";

    res.status(status).json({ message });
    throw err;
  });

  // importantly only setup vite in development and after
  // setting up all the other routes so the catch-all route
  // doesn't interfere with the other routes
  if (app.get("env") === "development") {
    await setupVite(app, server);
  } else {
    serveStatic(app);
  }

  // ALWAYS serve the app on port 5000
  // this serves both the API and the client.
  // It is the only port that is not firewalled.
  const port = process.env.PORT ? parseInt(process.env.PORT) : 5000;
  server.listen({
    port,
    host: "0.0.0.0", // Listen on all interfaces for Docker
    reusePort: true,
  }, () => {
    log(`Server running at http://0.0.0.0:${port}`);
    log(`Environment: ${process.env.NODE_ENV || 'development'}`);
  });
})();
