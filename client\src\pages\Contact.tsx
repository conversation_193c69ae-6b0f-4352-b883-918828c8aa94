import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { CheckCircle, AlertCircle, Mail, MapPin, Clock, ArrowRight, Send, MessageSquare } from 'lucide-react';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

// Form validation schema
const formSchema = z.object({
  name: z.string().min(2, { message: 'Name must be at least 2 characters' }),
  email: z.string().email({ message: 'Please enter a valid email address' }),
  subject: z.string().min(5, { message: 'Subject must be at least 5 characters' }),
  message: z.string().min(10, { message: 'Message must be at least 10 characters' }),
});

type FormValues = z.infer<typeof formSchema>;

export default function Contact() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'success' | 'error' | null>(null);

  useEffect(() => {
    // Update document title
    document.title = "Contact Us - browzy - Supercharge Your Browser with AI";

    // Add meta description
    const metaDescription = document.createElement('meta');
    metaDescription.name = 'description';
    metaDescription.content = 'Contact the browzy team with questions, feedback, or support requests. We\'re here to help you get the most out of your AI-powered browsing experience.';
    document.head.appendChild(metaDescription);

    window.scrollTo(0, 0);

    return () => {
      document.head.removeChild(metaDescription);
    };
  }, []);

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
      email: '',
      subject: '',
      message: '',
    },
  });

  async function onSubmit(data: FormValues) {
    setIsSubmitting(true);
    setSubmitStatus(null);

    try {
      // In a real application, you would send this data to your backend
      console.log('Form data submitted:', data);

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Simulate successful submission
      setSubmitStatus('success');
      form.reset();
    } catch (error) {
      console.error('Error submitting form:', error);
      setSubmitStatus('error');
    } finally {
      setIsSubmitting(false);
    }
  }

  // Animation variants
  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0, transition: { duration: 0.5 } }
  };

  return (
    <div className="min-h-screen dark:bg-black light:bg-white text-foreground overflow-hidden relative">
      {/* Grid pattern background */}
      <div className="grid-pattern absolute inset-0 opacity-30"></div>

      <Header />

      <div className="max-w-6xl mx-auto px-6 sm:px-8 lg:px-12 pt-28 md:pt-32 relative z-10">
        <main className="py-8 md:py-12">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center mb-16"
          >
            <h2 className="font-inter text-5xl md:text-6xl font-bold tracking-tight">
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary">Contact Us</span>
            </h2>
            <p className="font-inter text-lg md:text-xl text-muted-foreground mt-6 max-w-2xl mx-auto leading-relaxed">
              Have questions or feedback? We'd love to hear from you and help with any inquiries you might have.
              <span className="block text-sm text-secondary italic mt-2">(We promise our AI is only slightly judgmental)</span>
            </p>
          </motion.div>

          <section className="container max-w-5xl mx-auto px-4 py-8">
            <motion.div
              variants={container}
              initial="hidden"
              animate="show"
              className="grid grid-cols-1 md:grid-cols-2 gap-8 lg:gap-12"
            >
              {/* Contact Information Card */}
              <motion.div variants={item}>
                <motion.div 
                  className="bg-card rounded-xl overflow-hidden border border-muted shadow-xl relative group hover:border-primary/30 transition-all duration-500 h-full flex flex-col p-6 md:p-8"
                  whileHover={{
                    boxShadow: "0 10px 30px rgba(0, 166, 192, 0.2), 0 0 0 1px rgba(0, 166, 192, 0.1)"
                  }}
                >
                  <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-secondary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none"></div>
                  <div className="absolute top-0 right-0 w-64 h-64 bg-primary/10 rounded-full blur-[50px] -translate-y-1/2 translate-x-1/2 opacity-30"></div>

                  <div className="mb-6 relative z-10">
                    <div className="flex items-center mb-4">
                      <div className="w-12 h-12 rounded-xl flex items-center justify-center shadow-lg bg-gradient-to-br from-primary/20 to-secondary/20">
                        <MessageSquare className="h-6 w-6 text-primary" />
                      </div>
                      <h3 className="font-inter text-2xl font-semibold ml-4 group-hover:text-primary transition-colors duration-300">
                        Get in Touch
                      </h3>
                    </div>
                    <p className="text-muted-foreground mb-6 font-inter">
                      We're here to help and answer any questions you might have. Our team is dedicated to providing you with the best support possible.
                    </p>
                  </div>

                  <div className="space-y-6 mb-8 relative z-10">
                    <div className="flex items-start space-x-4 group/item hover:bg-background/50 p-3 rounded-lg transition-colors duration-300">
                      <div className="w-12 h-12 rounded-xl flex items-center justify-center bg-gradient-to-br from-primary/30 to-accent/20 shadow-md group-hover:item:shadow-lg transition-all duration-300">
                        <Mail className="h-6 w-6 text-primary" />
                      </div>
                      <div>
                        <h4 className="font-medium text-lg font-inter">Email</h4>
                        <a href="mailto:<EMAIL>" className="text-primary hover:underline transition-all font-inter">
                          <EMAIL>
                        </a>
                      </div>
                    </div>

                    <div className="flex items-start space-x-4 group/item hover:bg-background/50 p-3 rounded-lg transition-colors duration-300">
                      <div className="w-12 h-12 rounded-xl flex items-center justify-center bg-gradient-to-br from-secondary/30 to-primary/20 shadow-md group-hover:item:shadow-lg transition-all duration-300">
                        <MapPin className="h-6 w-6 text-secondary" />
                      </div>
                      <div>
                        <h4 className="font-medium text-lg font-inter">Location</h4>
                        <p className="text-muted-foreground font-inter">Mumbai, India</p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-4 group/item hover:bg-background/50 p-3 rounded-lg transition-colors duration-300">
                      <div className="w-12 h-12 rounded-xl flex items-center justify-center bg-gradient-to-br from-accent/30 to-primary/20 shadow-md group-hover:item:shadow-lg transition-all duration-300">
                        <Clock className="h-6 w-6 text-accent" />
                      </div>
                      <div>
                        <h4 className="font-medium text-lg font-inter">Business Hours</h4>
                        <p className="text-muted-foreground font-inter">Monday - Friday: 9am - 5pm IST</p>
                      </div>
                    </div>
                  </div>

                  <div className="mt-auto pt-6 border-t border-border/30 relative z-10">
                    <h4 className="font-medium text-lg mb-3 font-inter">Connect With Us</h4>
                    <div className="flex space-x-4">
                      {['twitter', 'linkedin', 'github', 'discord'].map((social) => (
                        <a
                          key={social}
                          href={`#${social}`}
                          className="w-10 h-10 rounded-lg bg-gradient-to-br from-primary/20 to-accent/10 flex items-center justify-center hover:from-primary/30 hover:to-accent/20 transition-all duration-300 shadow-sm hover:shadow-md hover:scale-110"
                        >
                          <span className="sr-only">{social}</span>
                          <div className="w-5 h-5 text-primary">
                            {social === 'twitter' && (
                              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                <path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z" />
                              </svg>
                            )}
                            {social === 'linkedin' && (
                              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                <path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z" />
                                <rect x="2" y="9" width="4" height="12" />
                                <circle cx="4" cy="4" r="2" />
                              </svg>
                            )}
                            {social === 'github' && (
                              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                <path d="M9 19c-5 1.5-5-2.5-7-3m14 6v-3.87a3.37 3.37 0 0 0-.94-2.61c3.14-.35 6.44-1.54 6.44-7A5.44 5.44 0 0 0 20 4.77 5.07 5.07 0 0 0 19.91 1S18.73.65 16 2.48a13.38 13.38 0 0 0-7 0C6.27.65 5.09 1 5.09 1A5.07 5.07 0 0 0 5 4.77a5.44 5.44 0 0 0-1.5 3.78c0 5.42 3.3 6.61 6.44 7A3.37 3.37 0 0 0 9 18.13V22" />
                              </svg>
                            )}
                            {social === 'discord' && (
                              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                <path d="M18 9a5 5 0 0 0-5-5H9a5 5 0 0 0-5 5v5a5 5 0 0 0 5 5h4" />
                                <circle cx="15" cy="12" r="1" />
                                <circle cx="9" cy="12" r="1" />
                              </svg>
                            )}
                          </div>
                        </a>
                      ))}
                    </div>
                  </div>
                </motion.div>
              </motion.div>

              {/* Contact Form Card */}
              <motion.div variants={item}>
                <motion.div 
                  className="bg-card rounded-xl overflow-hidden border border-muted shadow-xl relative group hover:border-primary/30 transition-all duration-500 h-full flex flex-col p-6 md:p-8"
                  whileHover={{
                    boxShadow: "0 10px 30px rgba(0, 166, 192, 0.2), 0 0 0 1px rgba(0, 166, 192, 0.1)"
                  }}
                >
                  <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-secondary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none"></div>
                  <div className="absolute bottom-0 left-0 w-64 h-64 bg-secondary/10 rounded-full blur-[50px] translate-y-1/2 -translate-x-1/2 opacity-30"></div>

                  <div className="relative z-10">
                    <div className="flex items-center mb-6">
                      <div className="w-12 h-12 rounded-xl flex items-center justify-center shadow-lg bg-gradient-to-br from-secondary/20 to-primary/20">
                        <Send className="h-6 w-6 text-secondary" />
                      </div>
                      <h3 className="font-inter text-2xl font-semibold ml-4 group-hover:text-primary transition-colors duration-300">
                        Send us a Message
                      </h3>
                    </div>

                    {submitStatus === 'success' && (
                      <Alert className="mb-6 bg-success/10 border-success/30 text-success backdrop-blur-sm">
                        <CheckCircle className="h-5 w-5" />
                        <AlertTitle className="text-lg font-medium font-inter">Success!</AlertTitle>
                        <AlertDescription className="text-success/90 font-inter">
                          Your message has been sent successfully. We'll get back to you soon.
                        </AlertDescription>
                      </Alert>
                    )}

                    {submitStatus === 'error' && (
                      <Alert className="mb-6 bg-destructive/10 border-destructive/30 text-destructive backdrop-blur-sm">
                        <AlertCircle className="h-5 w-5" />
                        <AlertTitle className="text-lg font-medium font-inter">Error!</AlertTitle>
                        <AlertDescription className="text-destructive/90 font-inter">
                          There was a problem sending your message. Please try again later.
                        </AlertDescription>
                      </Alert>
                    )}

                    <Form {...form}>
                      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-5">
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-5">
                          <FormField
                            control={form.control}
                            name="name"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-base font-inter text-muted-foreground font-mono text-sm">NAME</FormLabel>
                                <FormControl>
                                  <Input
                                    placeholder="Your name"
                                    className="h-11 bg-background/50 backdrop-blur-sm border-muted rounded-md p-3 text-foreground focus:border-primary focus:outline-none transition font-inter"
                                    {...field}
                                  />
                                </FormControl>
                                <FormMessage className="font-inter" />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="email"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-base font-inter text-muted-foreground font-mono text-sm">EMAIL</FormLabel>
                                <FormControl>
                                  <Input
                                    placeholder="Your email"
                                    className="h-11 bg-background/50 backdrop-blur-sm border-muted rounded-md p-3 text-foreground focus:border-primary focus:outline-none transition font-inter"
                                    {...field}
                                  />
                                </FormControl>
                                <FormMessage className="font-inter" />
                              </FormItem>
                            )}
                          />
                        </div>
                        <FormField
                          control={form.control}
                          name="subject"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-base font-inter text-muted-foreground font-mono text-sm">SUBJECT</FormLabel>
                              <FormControl>
                                <Input
                                  placeholder="Message subject"
                                  className="h-11 bg-background/50 backdrop-blur-sm border-muted rounded-md p-3 text-foreground focus:border-primary focus:outline-none transition font-inter"
                                  {...field}
                                />
                              </FormControl>
                              <FormMessage className="font-inter" />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name="message"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-base font-inter text-muted-foreground font-mono text-sm">MESSAGE</FormLabel>
                              <FormControl>
                                <Textarea
                                  placeholder="Your message"
                                  className="min-h-[150px] bg-background/50 backdrop-blur-sm border-muted rounded-md p-3 text-foreground focus:border-primary focus:outline-none transition font-inter"
                                  {...field}
                                />
                              </FormControl>
                              <FormMessage className="font-inter" />
                            </FormItem>
                          )}
                        />

                        <div className="relative group/button inline-block w-full">
                          <div className="absolute -inset-0.5 bg-gradient-to-r from-primary to-secondary rounded-md blur opacity-70 group-hover/button:opacity-100 transition duration-500 group-hover/button:duration-200 animate-pulse-slow"></div>
                          <Button
                            type="submit"
                            className="relative w-full h-12 text-base mt-2 bg-gradient-to-r from-primary to-secondary text-white rounded-md hover:shadow-xl transition-all duration-300 transform group-hover/button:scale-[1.01] font-inter font-medium"
                            disabled={isSubmitting}
                          >
                            {isSubmitting ? (
                              <div className="flex items-center">
                                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 0 1 8-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                Sending...
                              </div>
                            ) : (
                              <div className="flex items-center justify-center">
                                Send Message
                                <ArrowRight className="ml-2 h-5 w-5 group-hover/button:translate-x-1 transition-transform duration-300" />
                              </div>
                            )}
                          </Button>
                        </div>
                      </form>
                    </Form>
                  </div>
                </motion.div>
              </motion.div>
            </motion.div>
          </section>

          <div className="mt-24 mb-16 relative">
            <div className="h-px w-full bg-gradient-to-r from-transparent via-primary/40 to-transparent"></div>
            <div className="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 w-20 h-20">
              <div className="absolute inset-0 bg-gradient-to-r from-primary/30 to-accent/30 rounded-full blur-xl animate-pulse-slow"></div>
              <div className="absolute inset-3 bg-gradient-to-r from-primary/20 to-accent/20 rounded-full blur-md animate-pulse-slow-reverse"></div>
            </div>
          </div>

          <motion.div
            className="text-center max-w-3xl mx-auto px-4"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <h2 className="font-inter text-3xl md:text-4xl font-bold mb-6">
              Need Immediate Assistance?
            </h2>
            <p className="font-inter text-lg text-muted-foreground mb-8 max-w-2xl mx-auto">
              Our support team is available to help you with any questions or concerns you may have about our products or services.
              <span className="block text-sm text-secondary italic mt-2">(Response times may vary based on how interesting your question is)</span>
            </p>

            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <div className="relative group inline-block">
                <div className="absolute -inset-0.5 bg-gradient-to-r from-primary to-secondary rounded-xl blur opacity-70 group-hover:opacity-100 transition duration-500 group-hover:duration-200 animate-pulse-slow"></div>
                <Button
                  className="relative bg-gradient-to-r from-primary to-secondary text-white shadow-xl text-lg py-6 px-8 rounded-xl transition-all duration-300 transform hover:scale-105 font-inter"
                  size="lg"
                >
                  <Mail className="mr-3 h-5 w-5" />
                  Email Support
                </Button>
              </div>

              <div className="relative group inline-block">
                <div className="absolute -inset-0.5 bg-gradient-to-r from-secondary/50 to-secondary/30 rounded-xl blur opacity-30 group-hover:opacity-70 transition duration-500 group-hover:duration-200"></div>
                <Button
                  variant="outline"
                  className="relative border-2 border-secondary text-secondary hover:bg-secondary/10 text-lg py-6 px-8 rounded-xl transition-all duration-300 transform hover:scale-105 font-inter"
                  size="lg"
                >
                  <svg className="mr-3 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M15.05 5A5 5 0 0 1 19 8.95M15.05 1A9 9 0 0 1 23 8.94m-1 7.98v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z" />
                  </svg>
                  Call Us
                </Button>
              </div>
            </div>
          </motion.div>
        </main>
        <Footer />
      </div>
    </div>
  );
}
