import type { Express, Request, Response, NextFunction } from "express";
import { createServer, type Server } from "http";
import <PERSON><PERSON> from 'stripe';
import { supabaseAdmin } from "./supabase";
import {
  verifyToken,
  getUserById,
  updateUserPreferences,
  signInWithEmail,
  signUpWithEmail,
  signInWithGoogle,
  signOut,
  refreshSession
} from "./supabase";
import {
  getUserSubscription,
  createOrUpdateSubscription,
  downgradeToFreePlan,
  checkExpiredSubscriptions,
  initializeFreeSubscription,
  upgradePlan,
  extendSubscription
} from "./services/subscriptionService";
import {
  saveConnectionToken,
  validateConnectionToken,
  saveChatHistory,
  getChatHistory,
  getUserToken,
  deleteUserTokens
} from "./services/extensionService";
import { makeOpenRouterRequest, trackOpenRouterUsage } from "./services/openRouterService";
import { calculateEndDate } from "./models/subscription";
import { supabaseAdmin as supabase } from "./supabase";

/**
 * Get usage count for a user
 * @param userId User ID
 * @returns Object with count of usage
 */
async function getUserUsage(userId: string) {
  try {
    // Get the start of the current day in UTC
    const today = new Date();
    today.setUTCHours(0, 0, 0, 0);

    // Query the database for usage records from today only
    const { data, error } = await supabase
      .from('usage_records')
      .select('id')
      .eq('user_id', userId)
      .gte('created_at', today.toISOString());

    if (error) {
      console.error('Error getting user usage:', error);
      return { count: 0, error };
    }

    console.log(`User ${userId} has used ${data.length} requests today`);
    return { count: data.length, error: null };
  } catch (error) {
    console.error('Error getting user usage:', error);
    return { count: 0, error };
  }
}
import { recordUserConnection, recordUserDisconnection, getUserMetrics } from "./services/userConnectionService";
import { recordUsage, getUserUsageSummary, checkUsageLimit } from "./services/usageService";

// Authentication middleware
const authMiddleware = async (req: Request, res: Response, next: NextFunction) => {
  try {
    console.log('Auth middleware called');

    // Try to get token from Authorization header
    let token: string | undefined;
    const authHeader = req.headers.authorization;

    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.split(' ')[1];
      console.log('Found token in Authorization header');
    }

    // If no token in header, try to get from cookies
    if (!token && req.cookies) {
      console.log('Cookies received:', req.cookies);

      // Try to get from supabase-auth-token cookie
      if (req.cookies['supabase-auth-token']) {
        token = req.cookies['supabase-auth-token'];
        console.log('Found token in supabase-auth-token cookie');
      }

      // Try to get from supabase.auth.token cookie (JSON format)
      else if (req.cookies['supabase.auth.token']) {
        try {
          const tokenData = JSON.parse(req.cookies['supabase.auth.token']);
          if (tokenData && tokenData.access_token) {
            token = tokenData.access_token;
            console.log('Found token in supabase.auth.token cookie');
          }
        } catch (parseError) {
          console.error('Error parsing supabase.auth.token cookie:', parseError);
        }
      }
    }

    // If still no token, return error
    if (!token) {
      console.log('No token found in request');
      return res.status(401).json({ message: 'Authentication required' });
    }

    // Verify the token
    console.log('Verifying token...');
    const user = await verifyToken(token);

    if (!user) {
      console.log('Invalid or expired token');
      return res.status(401).json({ message: 'Invalid or expired token' });
    }

    console.log('Token verified successfully for user:', user.id);

    // Attach user to request object for use in route handlers
    (req as any).user = user;
    next();
  } catch (error) {
    console.error('Auth middleware error:', error);
    return res.status(500).json({ message: 'Server error during authentication' });
  }
};

// Initialize Stripe
console.log('Stripe Secret Key:', process.env.STRIPE_SECRET_KEY ? 'Key is set' : 'Key is not set');
// Fallback key in case environment variable is not loaded
const STRIPE_SECRET_KEY = process.env.STRIPE_SECRET_KEY || 'sk_test_51P0GFnSABghPrT7L2xLsN3KRJhMxEAbJ03oqZ5grcKJ8ac3jeT0ejPddPkZcwRNvAh0XKfQ8bOTnssWk3L2mrnZe00UDnKkJrP';
const stripe = new Stripe(STRIPE_SECRET_KEY);

export async function registerRoutes(app: Express): Promise<Server> {
  // Set up a scheduled task to check for expired subscriptions
  // Run every day at midnight
  setInterval(async () => {
    console.log('Running scheduled task: Checking for expired subscriptions');
    try {
      const count = await checkExpiredSubscriptions();
      console.log(`Downgraded ${count} expired subscriptions to free plan`);
    } catch (error) {
      console.error('Error checking expired subscriptions:', error);
    }
  }, 24 * 60 * 60 * 1000); // 24 hours
  // API status endpoint for testing connection
  app.get('/api/status', (_req, res) => {
    return res.status(200).json({
      status: 'online',
      message: 'Server is running and connected to the frontend',
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'development'
    });
  });

  // Contact form endpoint
  app.post('/api/contact', async (req, res) => {
    try {
      const { name, email, subject, message, consent } = req.body;

      // Validate the data
      if (!name || !email || !subject || !message || !consent) {
        return res.status(400).json({ message: "All fields are required, even if you don't want to fill them." });
      }

      // Email validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        return res.status(400).json({ message: "Invalid email format. We know you're trying to trick our AI." });
      }

      // In a production app, you would send an email or store the message
      // For now, we'll just return a success response
      console.log('Contact form submission:', { name, email, subject, message, consent });

      return res.status(200).json({
        message: "Message received! We'll get back to you... maybe.",
        status: "success"
      });
    } catch (error) {
      console.error('Error processing contact form:', error);
      return res.status(500).json({
        message: "Server error. It's not us, it's definitely you.",
        status: "error"
      });
    }
  });

  // Authentication endpoints

  // Sign in with email and password
  app.post('/api/auth/signin', async (req, res) => {
    try {
      const { email, password } = req.body;

      if (!email || !password) {
        return res.status(400).json({ message: 'Email and password are required' });
      }

      const { user, session, error } = await signInWithEmail(email, password);

      if (error) {
        return res.status(401).json({
          message: error.message || 'Authentication failed',
          error: error.message
        });
      }

      if (!user || !session) {
        return res.status(401).json({ message: 'Authentication failed' });
      }

      // Set cookies for 30-day persistence
      const expirationDate = new Date();
      expirationDate.setDate(expirationDate.getDate() + 30); // 30 days

      // Set access token cookie
      res.cookie('supabase-auth-token', session.access_token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax', // Changed from 'strict' to 'lax' for better cross-site persistence
        expires: expirationDate,
        path: '/',
        maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days in milliseconds
        signed: true // Sign the cookie for added security
      });

      // Set refresh token cookie
      res.cookie('supabase-auth-refresh', session.refresh_token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax', // Changed from 'strict' to 'lax'
        expires: expirationDate,
        path: '/',
        maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days in milliseconds
        signed: true // Sign the cookie for added security
      });

      // Set a non-httpOnly cookie to indicate the user is logged in
      // This can be read by the frontend to determine login state
      res.cookie('user-logged-in', 'true', {
        httpOnly: false,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax', // Changed from 'strict' to 'lax'
        expires: expirationDate,
        path: '/',
        maxAge: 30 * 24 * 60 * 60 * 1000 // 30 days in milliseconds
      });

      // Set a session cookie with user info
      res.cookie('user-session', JSON.stringify({
        id: user.id,
        email: user.email,
        lastLogin: new Date().toISOString()
      }), {
        httpOnly: false,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        expires: expirationDate,
        path: '/',
        maxAge: 30 * 24 * 60 * 60 * 1000 // 30 days in milliseconds
      });

      return res.status(200).json({
        message: 'Authentication successful',
        user: {
          id: user.id,
          email: user.email,
          created_at: user.created_at
        },
        session: {
          access_token: session.access_token,
          expires_at: session.expires_at
        }
      });
    } catch (error: any) {
      console.error('Error during sign in:', error);
      return res.status(500).json({
        message: 'Server error during authentication',
        error: error.message
      });
    }
  });

  // Sign up with email and password
  app.post('/api/auth/signup', async (req, res) => {
    try {
      const { email, password } = req.body;

      if (!email || !password) {
        return res.status(400).json({ message: 'Email and password are required' });
      }

      const { user, session, error } = await signUpWithEmail(email, password);

      if (error) {
        return res.status(400).json({
          message: error.message || 'Registration failed',
          error: error.message
        });
      }

      return res.status(200).json({
        message: 'Registration successful. Please check your email for verification.',
        user: user ? {
          id: user.id,
          email: user.email,
          created_at: user.created_at
        } : null,
        session: session ? {
          access_token: session.access_token,
          expires_at: session.expires_at
        } : null
      });
    } catch (error: any) {
      console.error('Error during sign up:', error);
      return res.status(500).json({
        message: 'Server error during registration',
        error: error.message
      });
    }
  });

  // Sign in with Google
  app.post('/api/auth/google', async (_req, res) => {
    try {
      const { url, error } = await signInWithGoogle();

      if (error) {
        return res.status(400).json({
          message: error.message || 'Google authentication failed',
          error: error.message
        });
      }

      if (!url) {
        return res.status(400).json({ message: 'Failed to generate Google authentication URL' });
      }

      return res.status(200).json({
        message: 'Google authentication initiated',
        url
      });
    } catch (error: any) {
      console.error('Error during Google sign in:', error);
      return res.status(500).json({
        message: 'Server error during Google authentication',
        error: error.message
      });
    }
  });

  // Sign out
  app.post('/api/auth/signout', async (req, res) => {
    try {
      const { token } = req.body;

      if (!token) {
        return res.status(400).json({ message: 'Token is required' });
      }

      const { success, error } = await signOut(token);

      if (error) {
        return res.status(400).json({
          message: error.message || 'Sign out failed',
          error: error.message
        });
      }

      // Clear all auth cookies
      const cookiesToClear = [
        'supabase-auth-token',
        'supabase-auth-refresh',
        'user-logged-in',
        'user-session',
        'remembered_user',
        'oauth_signin',
        'supabase.auth.token'
      ];

      cookiesToClear.forEach(cookieName => {
        // Clear with the same settings as when they were set
        res.clearCookie(cookieName, {
          httpOnly: cookieName.includes('token') || cookieName.includes('refresh'),
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'lax',
          path: '/',
          signed: cookieName.includes('token') || cookieName.includes('refresh')
        });

        console.log(`Cleared cookie: ${cookieName}`);
      });

      return res.status(200).json({
        message: 'Sign out successful',
        success
      });
    } catch (error: any) {
      console.error('Error during sign out:', error);
      return res.status(500).json({
        message: 'Server error during sign out',
        error: error.message
      });
    }
  });

  // Refresh session
  app.post('/api/auth/refresh', async (req, res) => {
    try {
      // Try to get refresh token from request body
      let refresh_token = req.body.refresh_token;

      // If not in body, try to get from cookies
      if (!refresh_token && req.cookies) {
        refresh_token = req.cookies['supabase-auth-refresh'];
      }

      if (!refresh_token) {
        return res.status(400).json({ message: 'Refresh token is required' });
      }

      const { session, error } = await refreshSession(refresh_token);

      if (error) {
        return res.status(401).json({
          message: error.message || 'Session refresh failed',
          error: error.message
        });
      }

      if (!session) {
        return res.status(401).json({ message: 'Session refresh failed' });
      }

      // Set cookies for 30-day persistence
      const expirationDate = new Date();
      expirationDate.setDate(expirationDate.getDate() + 30); // 30 days

      // Set access token cookie
      res.cookie('supabase-auth-token', session.access_token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax', // Changed from 'strict' to 'lax' for better cross-site persistence
        expires: expirationDate,
        path: '/',
        maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days in milliseconds
        signed: true // Sign the cookie for added security
      });

      // Set refresh token cookie
      res.cookie('supabase-auth-refresh', session.refresh_token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax', // Changed from 'strict' to 'lax'
        expires: expirationDate,
        path: '/',
        maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days in milliseconds
        signed: true // Sign the cookie for added security
      });

      // Set a non-httpOnly cookie to indicate the user is logged in
      // This can be read by the frontend to determine login state
      res.cookie('user-logged-in', 'true', {
        httpOnly: false,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax', // Changed from 'strict' to 'lax'
        expires: expirationDate,
        path: '/',
        maxAge: 30 * 24 * 60 * 60 * 1000 // 30 days in milliseconds
      });

      // Set a session cookie with session info
      res.cookie('user-session', JSON.stringify({
        sessionId: session.user.id,
        refreshedAt: new Date().toISOString()
      }), {
        httpOnly: false,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        expires: expirationDate,
        path: '/',
        maxAge: 30 * 24 * 60 * 60 * 1000 // 30 days in milliseconds
      });

      return res.status(200).json({
        message: 'Session refreshed successfully',
        session: {
          access_token: session.access_token,
          refresh_token: session.refresh_token,
          expires_at: session.expires_at
        }
      });
    } catch (error: any) {
      console.error('Error during session refresh:', error);
      return res.status(500).json({
        message: 'Server error during session refresh',
        error: error.message
      });
    }
  });

  // Verify token endpoint
  app.post('/api/auth/verify', async (req, res) => {
    try {
      const { token } = req.body;

      if (!token) {
        return res.status(400).json({ message: 'Token is required' });
      }

      const user = await verifyToken(token);

      if (!user) {
        return res.status(401).json({ message: 'Invalid or expired token' });
      }

      return res.status(200).json({
        message: 'Token is valid',
        user: {
          id: user.id,
          email: user.email,
          created_at: user.created_at
        }
      });
    } catch (error) {
      console.error('Error verifying token:', error);
      return res.status(500).json({ message: 'Server error during token verification' });
    }
  });

  // User profile endpoint (protected)
  app.get('/api/user/profile', authMiddleware, async (req, res) => {
    try {
      const user = (req as any).user;
      const profile = await getUserById(user.id);

      if (!profile) {
        return res.status(404).json({ message: 'User profile not found' });
      }

      return res.status(200).json({ profile });
    } catch (error) {
      console.error('Error getting user profile:', error);
      return res.status(500).json({ message: 'Server error getting user profile' });
    }
  });

  // Update user preferences endpoint (protected)
  app.put('/api/user/preferences', authMiddleware, async (req, res) => {
    try {
      const user = (req as any).user;
      const { preferences } = req.body;

      if (!preferences) {
        return res.status(400).json({ message: 'Preferences are required' });
      }

      const updatedProfile = await updateUserPreferences(user.id, preferences);

      if (!updatedProfile) {
        return res.status(500).json({ message: 'Failed to update preferences' });
      }

      return res.status(200).json({
        message: 'Preferences updated successfully',
        profile: updatedProfile
      });
    } catch (error) {
      console.error('Error updating preferences:', error);
      return res.status(500).json({ message: 'Server error updating preferences' });
    }
  });

  // Get user subscription (protected)
  app.get('/api/user/subscription', authMiddleware, async (req, res) => {
    try {
      const user = (req as any).user;
      const subscription = await getUserSubscription(user.id);

      if (!subscription) {
        // If no subscription exists, initialize a free subscription
        const freeSubscription = await initializeFreeSubscription(user.id);

        if (!freeSubscription) {
          return res.status(500).json({ message: 'Failed to initialize subscription' });
        }

        return res.status(200).json({ subscription: freeSubscription });
      }

      return res.status(200).json({ subscription });
    } catch (error: any) {
      console.error('Error getting user subscription:', error);
      return res.status(500).json({
        message: 'Server error getting user subscription',
        error: error.message
      });
    }
  });

  // Upgrade user plan (protected)
  app.post('/api/user/subscription/upgrade', authMiddleware, async (req, res) => {
    try {
      const user = (req as any).user;
      const { planId, stripeSubscriptionId, stripeCustomerId } = req.body;

      if (!planId) {
        return res.status(400).json({ message: 'Plan ID is required' });
      }

      // Validate plan ID
      if (planId !== 'pro' && planId !== 'ultimate' && planId !== 'free') {
        return res.status(400).json({ message: 'Invalid plan ID' });
      }

      // Upgrade the user's plan
      const updatedSubscription = await upgradePlan(
        user.id,
        planId,
        stripeSubscriptionId,
        stripeCustomerId
      );

      if (!updatedSubscription) {
        return res.status(500).json({ message: 'Failed to upgrade subscription' });
      }

      return res.status(200).json({
        message: 'Subscription upgraded successfully',
        subscription: updatedSubscription
      });
    } catch (error: any) {
      console.error('Error upgrading subscription:', error);
      return res.status(500).json({
        message: 'Server error upgrading subscription',
        error: error.message
      });
    }
  });

  // Extend user subscription (protected)
  app.post('/api/user/subscription/extend', authMiddleware, async (req, res) => {
    try {
      const user = (req as any).user;

      // Extend the user's subscription
      const updatedSubscription = await extendSubscription(user.id);

      if (!updatedSubscription) {
        return res.status(500).json({ message: 'Failed to extend subscription' });
      }

      return res.status(200).json({
        message: 'Subscription extended successfully',
        subscription: updatedSubscription
      });
    } catch (error: any) {
      console.error('Error extending subscription:', error);
      return res.status(500).json({
        message: 'Server error extending subscription',
        error: error.message
      });
    }
  });

  // Stripe payment endpoints

  // Create Stripe Checkout Session (authenticated)
  app.post('/api/payment/create-checkout-session', authMiddleware, async (req, res) => {
    try {
      const { priceId, planId } = req.body;

      if (!priceId) {
        return res.status(400).json({ message: 'Price ID is required' });
      }

      // Get the customer or create one if it doesn't exist
      const userId = (req as any).user.id;
      let customerId;

      // Check if customer exists in Stripe
      const customers = await stripe.customers.list({
        email: (req as any).user.email,
        limit: 1
      });

      if (customers.data.length > 0) {
        customerId = customers.data[0].id;
      } else {
        // Create a new customer
        const customer = await stripe.customers.create({
          email: (req as any).user.email,
          metadata: {
            userId
          }
        });
        customerId = customer.id;
      }

      // Create a Stripe Checkout Session
      const session = await stripe.checkout.sessions.create({
        payment_method_types: ['card'],
        line_items: [
          {
            price: priceId,
            quantity: 1,
          },
        ],
        mode: 'subscription',
        customer: customerId,
        success_url: `${process.env.CLIENT_URL || 'http://localhost:5173'}/payment-success?session_id={CHECKOUT_SESSION_ID}&plan=${planId}`,
        cancel_url: `${process.env.CLIENT_URL || 'http://localhost:5173'}/payment-cancel`,
        metadata: {
          userId,
          planId
        },
      });

      return res.status(200).json({
        sessionId: session.id,
        url: session.url
      });
    } catch (error: any) {
      console.error('Error creating checkout session:', error);
      return res.status(500).json({
        message: 'Server error creating checkout session',
        error: error.message
      });
    }
  });

  // Create Stripe Checkout Session (non-authenticated)
  app.post('/api/payment/create-checkout-session-public', async (req, res) => {
    try {
      const { priceId, planId, email } = req.body;

      if (!priceId) {
        return res.status(400).json({ message: 'Price ID is required' });
      }

      if (!email) {
        return res.status(400).json({ message: 'Email is required' });
      }

      // Create a Stripe Checkout Session without requiring authentication
      const session = await stripe.checkout.sessions.create({
        payment_method_types: ['card'],
        customer_email: email,
        line_items: [
          {
            price: priceId,
            quantity: 1,
          },
        ],
        mode: 'subscription',
        success_url: `${process.env.CLIENT_URL || 'http://localhost:5173'}/payment-success?session_id={CHECKOUT_SESSION_ID}&plan=${planId}`,
        cancel_url: `${process.env.CLIENT_URL || 'http://localhost:5173'}/payment-cancel`,
        metadata: {
          planId,
          email
        },
      });

      return res.status(200).json({
        sessionId: session.id,
        url: session.url
      });
    } catch (error: any) {
      console.error('Error creating public checkout session:', error);
      return res.status(500).json({
        message: 'Server error creating checkout session',
        error: error.message
      });
    }
  });

  // Webhook to handle Stripe events
  app.post('/api/payment/webhook', async (req, res) => {
    const sig = req.headers['stripe-signature'] as string;
    const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET;

    let event;

    try {
      // Get the raw body for webhook signature verification
      const payload = req.body;

      // Verify the webhook signature
      if (endpointSecret && sig) {
        event = stripe.webhooks.constructEvent(
          payload,
          sig,
          endpointSecret
        );
      } else {
        // For development without webhook signature verification
        // Parse the JSON body if it's not already parsed
        event = typeof payload === 'string' ? JSON.parse(payload) : payload;
      }

      // Handle the event
      switch (event.type) {
        case 'checkout.session.completed':
          const session = event.data.object;

          // Update user subscription in your database
          if (session.metadata && session.metadata.userId && session.metadata.planId) {
            try {
              console.log(`Updating subscription for user ${session.metadata.userId} to plan ${session.metadata.planId}`);

              // Get subscription details from Stripe
              let subscriptionId = session.subscription;
              let subscriptionData: any = null;

              if (subscriptionId) {
                subscriptionData = await stripe.subscriptions.retrieve(subscriptionId as string);
              }

              // Calculate start and end dates
              const startDate = new Date();
              let endDate;

              if (subscriptionData) {
                // Use the period end from Stripe
                endDate = new Date(subscriptionData.current_period_end * 1000);
              } else {
                // Calculate based on plan type
                endDate = calculateEndDate(session.metadata.planId, startDate);
              }

              // Update the subscription in our database
              const updatedSubscription = await createOrUpdateSubscription(
                session.metadata.userId,
                session.metadata.planId,
                subscriptionId as string,
                session.customer as string,
                startDate,
                endDate,
                false // Not set to cancel at period end
              );

              console.log('Updated subscription:', updatedSubscription);

              console.log(`Subscription updated for user ${session.metadata.userId} to plan ${session.metadata.planId}`);
            } catch (error) {
              console.error('Error updating user subscription:', error);
            }
          }
          break;

        case 'customer.subscription.updated':
          const updatedSubscription = event.data.object;
          try {
            // Get the customer ID
            const customerId = updatedSubscription.customer;

            // Find the user associated with this customer
            const { data: customers } = await supabaseAdmin
              .from('subscriptions')
              .select('user_id')
              .eq('stripe_customer_id', customerId)
              .limit(1);

            if (customers && customers.length > 0) {
              const userId = customers[0].user_id;

              // Update the subscription details
              await createOrUpdateSubscription(
                userId,
                updatedSubscription.metadata?.planId || (updatedSubscription.items.data[0]?.price.nickname?.toLowerCase() || 'pro'),
                updatedSubscription.id,
                customerId as string,
                new Date(updatedSubscription.current_period_start * 1000),
                new Date(updatedSubscription.current_period_end * 1000),
                updatedSubscription.cancel_at_period_end
              );

              console.log(`Subscription updated for customer ${customerId}`);
            }
          } catch (error) {
            console.error('Error handling subscription update:', error);
          }
          break;

        case 'customer.subscription.deleted':
          const deletedSubscription = event.data.object;
          try {
            // Get the customer ID
            const customerId = deletedSubscription.customer;

            // Find the user associated with this customer
            const { data: customers } = await supabaseAdmin
              .from('subscriptions')
              .select('user_id')
              .eq('stripe_customer_id', customerId)
              .limit(1);

            if (customers && customers.length > 0) {
              const userId = customers[0].user_id;

              // Downgrade to free plan
              await downgradeToFreePlan(userId);

              console.log(`User ${userId} downgraded to free plan due to subscription deletion`);
            }
          } catch (error) {
            console.error('Error handling subscription deletion:', error);
          }
          break;

        default:
          console.log(`Unhandled event type ${event.type}`);
      }

      res.status(200).json({ received: true });
    } catch (error: any) {
      console.error('Webhook error:', error);
      return res.status(400).send(`Webhook Error: ${error.message}`);
    }
  });

  // Keep the old payment intent endpoint for backward compatibility
  app.post('/api/payment/create-payment-intent', authMiddleware, async (req, res) => {
    try {
      const { amount, currency = 'usd', paymentMethodType = 'card', priceId, planId } = req.body;

      if (!priceId) {
        return res.status(400).json({ message: 'Price ID is required' });
      }

      // Get the customer or create one if it doesn't exist
      const userId = (req as any).user.id;
      let customerId;

      // Check if customer exists in Stripe
      const customers = await stripe.customers.list({
        email: (req as any).user.email,
        limit: 1
      });

      if (customers.data.length > 0) {
        customerId = customers.data[0].id;
      } else {
        // Create a new customer
        const customer = await stripe.customers.create({
          email: (req as any).user.email,
          metadata: {
            userId
          }
        });
        customerId = customer.id;
      }

      // Create a payment intent
      const paymentIntent = await stripe.paymentIntents.create({
        amount: Math.round(amount * 100), // Convert to cents
        currency,
        payment_method_types: [paymentMethodType],
        customer: customerId,
        metadata: {
          priceId,
          planId,
          userId
        }
      });

      return res.status(200).json({
        clientSecret: paymentIntent.client_secret,
        paymentIntentId: paymentIntent.id,
        customerId
      });
    } catch (error: any) {
      console.error('Error creating payment intent:', error);
      return res.status(500).json({
        message: 'Server error creating payment intent',
        error: error.message
      });
    }
  });

  // Update user subscription (authenticated)
  app.put('/api/user/subscription', authMiddleware, async (req, res) => {
    try {
      const user = (req as any).user;
      const { planId } = req.body;

      if (!planId) {
        return res.status(400).json({ message: 'Plan ID is required' });
      }

      // In a real implementation, you would update the user's subscription in your database
      // For now, we'll just log the update and return success
      console.log(`Updating subscription for user ${user.id} to plan ${planId}`);

      // Here you would update the user's subscription in your database
      // For example: await db.updateUserSubscription(user.id, planId);

      return res.status(200).json({
        message: 'Subscription updated successfully',
        subscription: {
          userId: user.id,
          planId,
          updatedAt: new Date().toISOString()
        }
      });
    } catch (error: any) {
      console.error('Error updating subscription:', error);
      return res.status(500).json({
        message: 'Server error updating subscription',
        error: error.message
      });
    }
  });

  // Update user subscription (non-authenticated)
  app.put('/api/user/subscription-public', async (req, res) => {
    try {
      const { userId, planId, sessionId } = req.body;

      if (!userId || !planId) {
        return res.status(400).json({ message: 'User ID and Plan ID are required' });
      }

      // In a real implementation, you would verify the session ID with Stripe
      // and update the user's subscription in your database
      console.log(`Updating subscription for user ${userId} to plan ${planId} from session ${sessionId}`);

      // Here you would update the user's subscription in your database
      // For example: await db.updateUserSubscription(userId, planId);

      return res.status(200).json({
        message: 'Subscription updated successfully',
        subscription: {
          userId,
          planId,
          updatedAt: new Date().toISOString()
        }
      });
    } catch (error: any) {
      console.error('Error updating subscription:', error);
      return res.status(500).json({
        message: 'Server error updating subscription',
        error: error.message
      });
    }
  });

  // Health check endpoint
  app.get('/api/health', (req, res) => {
    // Set CORS headers explicitly for extension requests
    res.header('Access-Control-Allow-Origin', req.headers.origin || '*');
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
    res.header('Access-Control-Allow-Credentials', 'true');

    return res.status(200).json({
      status: 'ok',
      message: 'Server is running',
      origin: req.headers.origin || 'unknown'
    });
  });

  // Extension connection endpoints

  // Get user metrics
  app.get('/api/extension/user/metrics', authMiddleware, async (req, res) => {
    try {
      // Set CORS headers explicitly for extension requests
      res.header('Access-Control-Allow-Origin', req.headers.origin || '*');
      res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
      res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
      res.header('Access-Control-Allow-Credentials', 'true');

      const user = (req as any).user;
      const userId = req.query.userId as string || user.id;

      // Check if the user is requesting their own metrics or is an admin
      if (userId !== user.id && !user.isAdmin) {
        return res.status(403).json({
          success: false,
          error: 'You are not authorized to view these metrics'
        });
      }

      // Get user metrics
      const result = await getUserMetrics(userId);

      if (!result.success) {
        console.error('Error getting user metrics:', result.error);
        return res.status(500).json({
          success: false,
          error: 'Failed to get user metrics'
        });
      }

      return res.status(200).json({
        success: true,
        metrics: result.metrics
      });
    } catch (error: any) {
      console.error('Error getting user metrics:', error);
      return res.status(500).json({
        success: false,
        error: 'Server error getting user metrics: ' + (error.message || 'Unknown error')
      });
    }
  });

  // Record extension disconnection
  app.post('/api/extension/disconnect', async (req, res) => {
    try {
      // Set CORS headers explicitly for extension requests
      res.header('Access-Control-Allow-Origin', req.headers.origin || '*');
      res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
      res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
      res.header('Access-Control-Allow-Credentials', 'true');

      console.log('Disconnect request received:', req.body);
      const { userId, connectionId } = req.body;

      if (!userId) {
        console.log('Disconnect failed: User ID is required');
        return res.status(400).json({
          success: false,
          error: 'User ID is required'
        });
      }

      // Record disconnection
      const result = await recordUserDisconnection(userId, connectionId);

      if (!result.success) {
        console.error('Error recording disconnection:', result.error);
        return res.status(500).json({
          success: false,
          error: 'Failed to record disconnection'
        });
      }

      console.log('Disconnection recorded successfully for user:', userId);
      return res.status(200).json({
        success: true,
        message: 'Disconnection recorded successfully'
      });
    } catch (error: any) {
      console.error('Error recording disconnection:', error);
      return res.status(500).json({
        success: false,
        error: 'Server error recording disconnection: ' + (error.message || 'Unknown error')
      });
    }
  });

  // Validate extension connection token
  app.post('/api/extension/token/validate', async (req, res) => {
    try {
      // Set CORS headers explicitly for extension requests
      res.header('Access-Control-Allow-Origin', req.headers.origin || '*');
      res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
      res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
      res.header('Access-Control-Allow-Credentials', 'true');

      console.log('Token validation request received:', req.body);
      const {
        token,
        extensionVersion,
        browserInfo,
        osInfo,
        deviceType,
        deviceFingerprint
      } = req.body;

      if (!token) {
        console.log('Token validation failed: Token is required');
        return res.status(400).json({
          success: false,
          error: 'Token is required'
        });
      }

      console.log('Validating token:', token);
      const result = await validateConnectionToken(token);
      console.log('Token validation result:', result);

      if (!result.valid) {
        console.log('Token validation failed:', result.error || 'Invalid or expired token');
        return res.status(401).json({
          success: false,
          error: result.error || 'Invalid or expired token'
        });
      }

      // Record user connection data
      const userId = result.userId as string;
      const connectionData = {
        userId,
        tokenId: result.tokenId,
        extensionVersion,
        browserName: browserInfo?.name,
        browserVersion: browserInfo?.version,
        osName: osInfo?.name,
        osVersion: osInfo?.version,
        deviceType,
        ipAddress: req.ip,
        userAgent: req.headers['user-agent'],
        deviceFingerprint
      };

      // Track device fingerprint if provided
      if (deviceFingerprint) {
        try {
          // Import device tracking service
          const { trackDeviceFingerprint } = await import('./services/deviceTrackingService');

          // Track this device fingerprint
          await trackDeviceFingerprint(userId, {
            fingerprint: deviceFingerprint,
            ipAddress: req.ip || 'unknown',
            userAgent: req.headers['user-agent'] as string,
            browserName: browserInfo?.name,
            browserVersion: browserInfo?.version,
            osName: osInfo?.name,
            osVersion: osInfo?.version,
            deviceType
          });
        } catch (deviceError) {
          console.error('Error tracking device fingerprint:', deviceError);
          // Continue even if device tracking fails
        }
      }

      console.log('Recording user connection data:', connectionData);
      const connectionResult = await recordUserConnection(connectionData);

      if (!connectionResult.success) {
        console.error('Error recording user connection:', connectionResult.error);
        // Continue anyway, don't fail the validation
      } else {
        console.log('User connection recorded successfully with ID:', connectionResult.connectionId);
      }

      console.log('Token validation successful for user:', userId);
      return res.status(200).json({
        success: true,
        userId,
        username: result.username,
        connectionId: connectionResult.connectionId,
        lastUsed: result.lastUsed
      });
    } catch (error: any) {
      console.error('Error validating extension token:', error);
      return res.status(500).json({
        success: false,
        error: 'Server error validating token: ' + (error.message || 'Unknown error')
      });
    }
  });

  // CORS preflight for token generation
  app.options('/api/extension/token/generate', (req, res) => {
    res.header('Access-Control-Allow-Origin', req.headers.origin || '*');
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
    res.header('Access-Control-Allow-Credentials', 'true');
    res.status(200).send();
  });

  // Get user's current extension token (protected)
  app.get('/api/extension/token', authMiddleware, async (req, res) => {
    try {
      // Set CORS headers explicitly for extension requests
      res.header('Access-Control-Allow-Origin', req.headers.origin || '*');
      res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
      res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
      res.header('Access-Control-Allow-Credentials', 'true');

      console.log('Token retrieval request received');
      const user = (req as any).user;
      console.log('User authenticated:', user.id);

      const result = await getUserToken(user.id);

      if (!result.success) {
        return res.status(500).json({
          success: false,
          error: 'Failed to get token'
        });
      }

      return res.status(200).json({
        success: true,
        token: result.token || null,
        tokenId: result.tokenId || null,
        createdAt: result.createdAt || null,
        lastUsed: result.lastUsed || null
      });
    } catch (error: any) {
      console.error('Error getting extension token:', error);
      return res.status(500).json({
        success: false,
        error: 'Server error getting token'
      });
    }
  });

  // Revoke user's extension token (protected)
  app.delete('/api/extension/token', authMiddleware, async (req, res) => {
    try {
      // Set CORS headers explicitly for extension requests
      res.header('Access-Control-Allow-Origin', req.headers.origin || '*');
      res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
      res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
      res.header('Access-Control-Allow-Credentials', 'true');

      console.log('Token revocation request received');
      const user = (req as any).user;
      console.log('User authenticated:', user.id);

      const result = await deleteUserTokens(user.id);

      if (!result.success) {
        return res.status(500).json({
          success: false,
          error: 'Failed to revoke token'
        });
      }

      return res.status(200).json({
        success: true,
        message: 'Token revoked successfully'
      });
    } catch (error: any) {
      console.error('Error revoking extension token:', error);
      return res.status(500).json({
        success: false,
        error: 'Server error revoking token'
      });
    }
  });

  // Generate extension connection token (protected)
  app.post('/api/extension/token/generate', authMiddleware, async (req, res) => {
    try {
      // Set CORS headers explicitly for extension requests
      res.header('Access-Control-Allow-Origin', req.headers.origin || '*');
      res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
      res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
      res.header('Access-Control-Allow-Credentials', 'true');

      console.log('Token generation request received:', req.body);
      const user = (req as any).user;
      console.log('User authenticated:', user.id);
      const { token } = req.body;

      if (!token) {
        return res.status(400).json({
          success: false,
          error: 'Token is required'
        });
      }

      const result = await saveConnectionToken(user.id, token);

      if (!result.success) {
        return res.status(500).json({
          success: false,
          error: 'Failed to save token'
        });
      }

      return res.status(200).json({
        success: true,
        message: 'Token saved successfully'
      });
    } catch (error: any) {
      console.error('Error generating extension token:', error);
      return res.status(500).json({
        success: false,
        error: 'Server error generating token'
      });
    }
  });

  // CORS preflight for OpenRouter API proxy
  app.options('/api/extension/openrouter/chat', (req, res) => {
    res.header('Access-Control-Allow-Origin', req.headers.origin || '*');
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
    res.header('Access-Control-Allow-Credentials', 'true');
    res.status(200).send();
  });

  // OpenRouter API proxy for GPT-3.5 16k model
  app.post('/api/extension/openrouter/chat', async (req, res) => {
    try {
      // Set CORS headers explicitly for extension requests
      res.header('Access-Control-Allow-Origin', req.headers.origin || '*');
      res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
      res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
      res.header('Access-Control-Allow-Credentials', 'true');

      console.log('OpenRouter API proxy request received');
      const { token, data } = req.body;

      if (!token) {
        console.log('OpenRouter API proxy failed: Token is required');
        return res.status(400).json({
          success: false,
          error: 'Token is required'
        });
      }

      if (!data) {
        console.log('OpenRouter API proxy failed: Request data is required');
        return res.status(400).json({
          success: false,
          error: 'Request data is required'
        });
      }

      // Validate the token first
      console.log('Validating token for OpenRouter API proxy:', token);
      const validation = await validateConnectionToken(token);
      console.log('Token validation result for OpenRouter API proxy:', validation);

      if (!validation.valid || !validation.userId) {
        console.log('OpenRouter API proxy failed: Invalid or expired token');
        return res.status(401).json({
          success: false,
          error: 'Invalid or expired token'
        });
      }

      // Check if user has reached their account limit
      try {
        const subscription = await getUserSubscription(validation.userId);
        const planId = subscription?.plan_id || 'free';

        // No usage limits for any users
        console.log(`User ${validation.userId} has plan: ${planId}, no usage limits applied`);
        // Usage tracking is still maintained for analytics purposes only
      } catch (error) {
        console.error('Error checking usage limits:', error);
        // Continue even if there's an error checking limits
      }

      // Make the request to OpenRouter
      console.log('Making OpenRouter API request for user:', validation.userId);
      console.log('Using model:', data.model);
      const openRouterResponse = await makeOpenRouterRequest(data);

      if (!openRouterResponse.success) {
        console.error('OpenRouter API request failed:', openRouterResponse.error);
        return res.status(500).json({
          success: false,
          error: openRouterResponse.error || 'OpenRouter API request failed'
        });
      }

      // Record usage in the database
      try {
        // Insert a usage record
        const { error: usageError } = await supabase
          .from('usage_records')
          .insert({
            user_id: validation.userId,
            service: 'openrouter',
            model: data.model || 'gpt-3.5-turbo-16k',
            request_type: 'chat',
            created_at: new Date().toISOString()
          });

        if (usageError) {
          console.error('Error recording usage:', usageError);
        } else {
          console.log(`Recorded usage for user ${validation.userId}`);
        }
      } catch (usageError) {
        console.error('Exception recording usage:', usageError);
        // Continue even if recording usage fails
      }

      // Track token usage if available in the response
      if (openRouterResponse.data && openRouterResponse.data.usage) {
        const { total_tokens } = openRouterResponse.data.usage;
        if (total_tokens) {
          await trackOpenRouterUsage(validation.userId, total_tokens);
        }
      }

      console.log('OpenRouter API request successful');
      return res.status(200).json({
        success: true,
        data: openRouterResponse.data
      });
    } catch (error) {
      console.error('Error in OpenRouter API proxy:', error);
      return res.status(500).json({
        success: false,
        error: 'Server error in OpenRouter API proxy'
      });
    }
  });

  // Save chat history from extension (protected)
  app.post('/api/extension/chat/history', async (req, res) => {
    try {
      // Set CORS headers explicitly for extension requests
      res.header('Access-Control-Allow-Origin', req.headers.origin || '*');
      res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
      res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
      res.header('Access-Control-Allow-Credentials', 'true');

      console.log('Chat history save request received');
      const { token, messages, deviceFingerprint } = req.body;
      const ipAddress = req.ip;

      if (!token) {
        console.log('Chat history save failed: Token is required');
        return res.status(400).json({
          success: false,
          error: 'Token is required'
        });
      }

      if (!messages || !Array.isArray(messages)) {
        console.log('Chat history save failed: Valid messages array is required');
        return res.status(400).json({
          success: false,
          error: 'Valid messages array is required'
        });
      }

      // Validate the token first
      console.log('Validating token for chat history save:', token);
      const validation = await validateConnectionToken(token);
      console.log('Token validation result for chat history save:', validation);

      if (!validation.valid || !validation.userId) {
        console.log('Chat history save failed: Invalid or expired token');
        return res.status(401).json({
          success: false,
          error: 'Invalid or expired token'
        });
      }

      // Save the chat history
      const result = await saveChatHistory(validation.userId, messages);

      if (!result.success) {
        return res.status(500).json({
          success: false,
          error: 'Failed to save chat history'
        });
      }

      // Record usage for each message
      try {
        // Get user's subscription
        const subscription = await getUserSubscription(validation.userId);
        const planId = subscription?.plan_id || 'free';

        // Track device fingerprint if provided (for analytics only)
        if (deviceFingerprint) {
          try {
            // Import device tracking service
            const {
              trackDeviceFingerprint
            } = await import('./services/deviceTrackingService');

            // Track this device fingerprint for analytics only
            await trackDeviceFingerprint(validation.userId, {
              fingerprint: deviceFingerprint,
              ipAddress: ipAddress || 'unknown'
            });

            // No usage limits are enforced
            console.log(`Device ${deviceFingerprint} tracked for user ${validation.userId} (no limits applied)`);

          } catch (deviceError) {
            console.error('Error tracking device:', deviceError);
            // Continue even if device tracking fails
          }
        }

        // No usage limits for any users
        // We still track usage for analytics purposes, but don't enforce limits

        // Count AI messages only (user sends a message, AI responds)
        const aiMessages = messages.filter(msg => msg.sender === 'ai');

        if (aiMessages.length > 0) {
          // Record usage
          await recordUsage(
            validation.userId,
            'chat',
            aiMessages.length,
            planId
          );
        }
      } catch (usageError) {
        console.error('Error recording usage for chat history:', usageError);
        // Continue even if usage recording fails
      }

      return res.status(200).json({
        success: true,
        message: 'Chat history saved successfully'
      });
    } catch (error: any) {
      console.error('Error saving chat history:', error);
      return res.status(500).json({
        success: false,
        error: 'Server error saving chat history'
      });
    }
  });

  // Get chat history (protected by token or auth)
  app.get('/api/extension/chat/history', async (req, res) => {
    try {
      // Set CORS headers explicitly for extension requests
      res.header('Access-Control-Allow-Origin', req.headers.origin || '*');
      res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
      res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
      res.header('Access-Control-Allow-Credentials', 'true');

      console.log('Chat history get request received');

      let userId: string;

      // Get token from query parameter
      const token = req.query.token as string;
      if (!token) {
        console.log('Chat history get failed: Token is required');
        return res.status(400).json({
          success: false,
          error: 'Token is required'
        });
      }

      // Validate the token
      console.log('Validating token for chat history get:', token);
      const validation = await validateConnectionToken(token);
      console.log('Token validation result for chat history get:', validation);

      if (!validation.valid || !validation.userId) {
        console.log('Chat history get failed: Invalid or expired token');
        return res.status(401).json({
          success: false,
          error: 'Invalid or expired token'
        });
      }

      userId = validation.userId;
      const limit = req.query.limit ? parseInt(req.query.limit as string) : 100;

      const result = await getChatHistory(userId, limit);

      if (!result.success) {
        return res.status(500).json({
          success: false,
          error: 'Failed to get chat history'
        });
      }

      return res.status(200).json({
        success: true,
        messages: result.messages || []
      });
    } catch (error: any) {
      console.error('Error getting chat history:', error);
      return res.status(500).json({
        success: false,
        error: 'Server error getting chat history'
      });
    }
  });

  // Get user AI preferences (protected by token)
  app.post('/api/extension/user/ai-preferences', async (req, res) => {
    try {
      // Set CORS headers explicitly for extension requests
      res.header('Access-Control-Allow-Origin', req.headers.origin || '*');
      res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
      res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
      res.header('Access-Control-Allow-Credentials', 'true');

      console.log('User AI preferences request received:', req.body);
      const { token } = req.body;

      if (!token) {
        console.log('User AI preferences request failed: Token is required');
        return res.status(400).json({
          success: false,
          error: 'Token is required'
        });
      }

      // Validate the token first
      console.log('Validating token for user AI preferences:', token);
      const validation = await validateConnectionToken(token);
      console.log('Token validation result for user AI preferences:', validation);

      if (!validation.valid || !validation.userId) {
        console.log('User AI preferences request failed: Invalid or expired token');
        return res.status(401).json({
          success: false,
          error: 'Invalid or expired token'
        });
      }

      // Get the user's profile
      const profile = await getUserById(validation.userId);

      if (!profile) {
        // If no profile exists, return default preferences
        return res.status(200).json({
          success: true,
          preferences: {
            ai: {
              provider: 'openai',
              model: 'gpt-3.5-turbo',
              fallbackModel: 'gpt-3.5-turbo'
            }
          }
        });
      }

      // Return the user's AI preferences
      return res.status(200).json({
        success: true,
        preferences: profile.preferences || {
          ai: {
            provider: 'openai',
            model: 'gpt-3.5-turbo',
            fallbackModel: 'gpt-3.5-turbo'
          }
        }
      });
    } catch (error: any) {
      console.error('Error getting user AI preferences:', error);
      return res.status(500).json({
        success: false,
        error: 'Server error getting user AI preferences'
      });
    }
  });

  // Get user plan for extension (protected by token)
  app.post('/api/extension/user/plan', async (req, res) => {
    try {
      // Set CORS headers explicitly for extension requests
      res.header('Access-Control-Allow-Origin', req.headers.origin || '*');
      res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
      res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
      res.header('Access-Control-Allow-Credentials', 'true');

      console.log('User plan request received:', req.body);
      const { token } = req.body;

      if (!token) {
        console.log('User plan request failed: Token is required');
        return res.status(400).json({
          success: false,
          error: 'Token is required'
        });
      }

      // Validate the token first
      console.log('Validating token for user plan:', token);
      const validation = await validateConnectionToken(token);
      console.log('Token validation result for user plan:', validation);

      if (!validation.valid || !validation.userId) {
        console.log('User plan request failed: Invalid or expired token');
        return res.status(401).json({
          success: false,
          error: 'Invalid or expired token'
        });
      }

      // Get the user's subscription
      const subscription = await getUserSubscription(validation.userId);

      if (!subscription) {
        // If no subscription exists, return free plan
        return res.status(200).json({
          success: true,
          plan: {
            id: 'premium',
            name: 'Premium',
            features: {
              maxRequestsPerDay: -1, // unlimited
              smartSearch: true,     // all features enabled
              multiTabSearch: true,
              exportHistory: true
            }
          }
        });
      }

      // Return plan details based on subscription
      // All plans have unlimited requests and all features
      const planFeatures = {
        free: {
          maxRequestsPerDay: -1, // unlimited for all users
          smartSearch: true,     // all features enabled for all users
          multiTabSearch: true,
          exportHistory: true
        },
        pro: {
          maxRequestsPerDay: -1, // unlimited
          smartSearch: true,
          multiTabSearch: true,
          exportHistory: true
        },
        ultimate: {
          maxRequestsPerDay: -1, // unlimited
          smartSearch: true,
          multiTabSearch: true,
          exportHistory: true
        }
      };

      return res.status(200).json({
        success: true,
        plan: {
          id: subscription.plan_id,
          name: subscription.plan_id.charAt(0).toUpperCase() + subscription.plan_id.slice(1),
          features: planFeatures[subscription.plan_id as keyof typeof planFeatures] || planFeatures.free
        }
      });
    } catch (error: any) {
      console.error('Error getting user plan:', error);
      return res.status(500).json({
        success: false,
        error: 'Server error getting user plan'
      });
    }
  });

  // Usage tracking endpoints

  // Get user usage summary (protected)
  app.get('/api/usage/summary', authMiddleware, async (req, res) => {
    try {
      const user = (req as any).user;

      const { success, summary } = await getUserUsageSummary(user.id);

      if (!success) {
        return res.status(500).json({
          success: false,
          error: 'Failed to get usage summary'
        });
      }

      return res.status(200).json({
        success: true,
        summary
      });
    } catch (error: any) {
      console.error('Error getting usage summary:', error);
      return res.status(500).json({
        success: false,
        error: 'Server error getting usage summary'
      });
    }
  });

  // Record usage (protected)
  app.post('/api/usage/record', authMiddleware, async (req, res) => {
    try {
      const user = (req as any).user;
      const { requestType, requestCount } = req.body;

      if (!requestType) {
        return res.status(400).json({
          success: false,
          error: 'Request type is required'
        });
      }

      // Get user's subscription
      const subscription = await getUserSubscription(user.id);
      const planId = subscription?.plan_id || 'free';

      // No usage limits check - all users have unlimited usage
      // We still track usage for analytics purposes, but don't enforce limits

      // Record the usage
      const { success } = await recordUsage(
        user.id,
        requestType,
        requestCount || 1,
        planId
      );

      if (!success) {
        return res.status(500).json({
          success: false,
          error: 'Failed to record usage'
        });
      }

      return res.status(200).json({
        success: true,
        message: 'Usage recorded successfully'
      });
    } catch (error: any) {
      console.error('Error recording usage:', error);
      return res.status(500).json({
        success: false,
        error: 'Server error recording usage'
      });
    }
  });

  // Check usage limit (protected)
  app.post('/api/usage/check-limit', authMiddleware, async (req, res) => {
    try {
      const user = (req as any).user;
      const { deviceFingerprint } = req.body;
      const ipAddress = req.ip;

      console.log(`Checking usage limit for user ${user.id} with device fingerprint ${deviceFingerprint} and IP ${ipAddress}`);

      // No usage limits for any users
      // Always return success=true and hasReachedLimit=false
      const hasReachedLimit = false;

      // Get user's subscription for informational purposes only
      const subscription = await getUserSubscription(user.id);
      const planId = subscription?.plan_id || 'free';

      // No device or IP limits for any users
      let deviceLimitReached = false;
      let ipLimitReached = false;
      let multipleAccountsDetected = false;

      // Still track device fingerprints for analytics, but don't enforce limits
      if (deviceFingerprint) {
        try {
          // Import device tracking service
          const {
            checkDeviceUsageLimit,
            checkIpUsageLimit,
            checkMultipleAccountsOnDevice,
            checkMultipleAccountsOnIp,
            trackDeviceFingerprint
          } = await import('./services/deviceTrackingService');

          // Track this device fingerprint
          await trackDeviceFingerprint(user.id, {
            fingerprint: deviceFingerprint,
            ipAddress: ipAddress || 'unknown'
          });

          // Check device usage limit
          const deviceLimitResult = await checkDeviceUsageLimit(deviceFingerprint);
          deviceLimitReached = deviceLimitResult.success && deviceLimitResult.hasReachedLimit;

          if (deviceLimitReached) {
            console.log(`Device ${deviceFingerprint} has reached usage limit: ${deviceLimitResult.requestCount} requests`);
          }

          // Check IP usage limit
          if (ipAddress) {
            const ipLimitResult = await checkIpUsageLimit(ipAddress);
            ipLimitReached = ipLimitResult.success && ipLimitResult.hasReachedLimit;

            if (ipLimitReached) {
              console.log(`IP ${ipAddress} has reached usage limit: ${ipLimitResult.requestCount} requests, ${ipLimitResult.accountCount} accounts`);
            }
          }

          // Check for multiple accounts on this device
          const multipleAccountsResult = await checkMultipleAccountsOnDevice(deviceFingerprint, user.id);
          multipleAccountsDetected = multipleAccountsResult.success && multipleAccountsResult.isMultipleAccounts && multipleAccountsResult.accountCount > 3;

          if (multipleAccountsDetected) {
            console.log(`Multiple accounts (${multipleAccountsResult.accountCount}) detected on device ${deviceFingerprint}`);
          }

          // Also check for multiple accounts on this IP
          if (ipAddress) {
            const multipleIpAccountsResult = await checkMultipleAccountsOnIp(ipAddress, user.id);
            // If more than 5 accounts on this IP, flag it
            if (multipleIpAccountsResult.success && multipleIpAccountsResult.accountCount > 5) {
              multipleAccountsDetected = true;
              console.log(`Multiple accounts (${multipleIpAccountsResult.accountCount}) detected on IP ${ipAddress}`);
            }
          }
        } catch (deviceError) {
          console.error('Error checking device limits:', deviceError);
          // Continue with account-level checks even if device checks fail
        }
      }

      // Get user's usage summary
      const { success: summarySuccess, summary, error: summaryError } = await getUserUsageSummary(user.id);

      if (!summarySuccess) {
        console.error('Error getting usage summary:', summaryError);
      }

      return res.status(200).json({
        success: true,
        hasReachedLimit,
        deviceLimitReached,
        ipLimitReached,
        multipleAccountsDetected,
        requestsUsed: summarySuccess ? summary?.monthly.total : undefined,
        plan: planId
      });
    } catch (error: any) {
      console.error('Error checking usage limit:', error);
      return res.status(500).json({
        success: false,
        error: 'Server error checking usage limit'
      });
    }
  });

  // Legacy endpoint for backward compatibility
  app.get('/api/usage/check-limit', authMiddleware, async (_req, res) => {
    try {
      // No need to get user ID since we're not checking limits

      // No usage limits for any users
      // Always return success=true and hasReachedLimit=false

      return res.status(200).json({
        success: true,
        hasReachedLimit: false
      });
    } catch (error: any) {
      console.error('Error checking usage limit:', error);
      return res.status(500).json({
        success: false,
        error: 'Server error checking usage limit'
      });
    }
  });

  // Get subscription plans
  app.get('/api/payment/plans', async (_req, res) => {
    try {
      // In a real implementation, you would fetch plans from Stripe
      // For now, we'll return hardcoded plans with actual Stripe price IDs
      const plans = [
        {
          id: 'free',
          name: 'Free',
          description: 'Basic features for casual browsing with a side of sass.',
          price: 0,
          currency: 'usd',
          interval: 'month',
          priceId: '', // Free plan has no price ID
          features: [
            { text: 'Unlimited requests', included: true },
            { text: 'Scan any webpage', included: true },
            { text: 'Basic content analysis', included: true },
            { text: 'Smart search feature', included: false },
            { text: 'Search across multiple tabs', included: false },
            { text: 'Export chat history', included: false },
          ]
        },
        {
          id: 'pro',
          name: 'Pro',
          description: 'Full access to all features with maximum judgment.',
          price: 20,
          currency: 'usd',
          interval: 'month',
          priceId: 'price_1RLGJvSABghPrT7Lx1w689k3', // Stripe price ID for $20
          features: [
            { text: 'Unlimited requests', included: true },
            { text: 'Scan any webpage', included: true },
            { text: 'Smart search feature', included: true },
            { text: 'Search across multiple tabs', included: true },
            { text: 'Export chat history', included: true },
            { text: 'Advanced content analysis', included: true },
          ]
        },
        {
          id: 'ultimate',
          name: 'Ultimate',
          description: 'The most comprehensive package for serious critics.',
          price: 200,
          currency: 'usd',
          interval: 'year',
          priceId: 'price_1RLGO3SABghPrT7Lw0jRLe9C', // Stripe price ID for $200
          features: [
            { text: 'Unlimited requests', included: true },
            { text: 'Everything in Pro plan', included: true },
            { text: 'Exclusive premium features', included: true },
            { text: 'Advanced AI-powered analysis', included: true },
            { text: 'Early access to new features', included: true },
            { text: 'Priority 24/7 support', included: true },
          ]
        }
      ];

      return res.status(200).json({ plans });
    } catch (error: any) {
      console.error('Error fetching plans:', error);
      return res.status(500).json({
        message: 'Server error fetching plans',
        error: error.message
      });
    }
  });

  const httpServer = createServer(app);

  return httpServer;
}
