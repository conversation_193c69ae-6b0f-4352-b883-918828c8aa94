import { spawn } from 'child_process';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  underscore: '\x1b[4m',
  blink: '\x1b[5m',
  reverse: '\x1b[7m',
  hidden: '\x1b[8m',

  fg: {
    black: '\x1b[30m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m',
    white: '\x1b[37m',
    crimson: '\x1b[38m'
  },

  bg: {
    black: '\x1b[40m',
    red: '\x1b[41m',
    green: '\x1b[42m',
    yellow: '\x1b[43m',
    blue: '\x1b[44m',
    magenta: '\x1b[45m',
    cyan: '\x1b[46m',
    white: '\x1b[47m',
    crimson: '\x1b[48m'
  }
};

// Check if server .env file exists, create it if not
const serverEnvPath = path.join(__dirname, 'server', '.env');
if (!fs.existsSync(serverEnvPath)) {
  console.log(`${colors.fg.yellow}Creating server .env file...${colors.reset}`);
  const envContent = `# Supabase credentials
SUPABASE_URL=your_supabase_url_here
SUPABASE_SERVICE_KEY=your_service_key_here
SUPABASE_ANON_KEY=your_anon_key_here

# Database connection
DATABASE_URL=your_database_url_here

# Server settings
PORT=5000
NODE_ENV=development
CLIENT_URL=http://localhost:5173`;

  fs.writeFileSync(serverEnvPath, envContent);
  console.log(`${colors.fg.green}Server .env file created successfully${colors.reset}`);
}

// Function to start a process
function startProcess(name, command, args, options = {}) {
  const color = options.color || colors.fg.white;
  const prefix = `${color}[${name}]${colors.reset}`;

  console.log(`${prefix} Starting ${name}...`);

  const proc = spawn(command, args, {
    shell: true,
    cwd: options.cwd || __dirname,
    env: { ...options.env }
  });

  proc.stdout.on('data', (data) => {
    const lines = data.toString().trim().split('\n');
    lines.forEach(line => {
      console.log(`${prefix} ${line}`);
    });
  });

  proc.stderr.on('data', (data) => {
    const lines = data.toString().trim().split('\n');
    lines.forEach(line => {
      console.error(`${prefix} ${colors.fg.red}${line}${colors.reset}`);
    });
  });

  proc.on('close', (code) => {
    if (code !== 0) {
      console.log(`${prefix} ${colors.fg.red}Process exited with code ${code}${colors.reset}`);
    } else {
      console.log(`${prefix} Process exited normally`);
    }
  });

  return proc;
}

// Start the server
const server = startProcess('Server', 'npm', ['run', 'dev'], {
  cwd: __dirname,
  color: colors.fg.cyan
});

// Wait a bit for the server to start before starting the client
setTimeout(() => {
  // Start the client
  const client = startProcess('Client', 'npm', ['run', 'frontend'], {
    cwd: __dirname,
    color: colors.fg.green
  });

  // Handle process termination
  process.on('SIGINT', () => {
    console.log(`\n${colors.fg.yellow}Shutting down...${colors.reset}`);
    server.kill();
    client.kill();
    process.exit(0);
  });
}, 2000);

console.log(`${colors.fg.magenta}${colors.bright}Starting development environment...${colors.reset}`);
console.log(`${colors.fg.yellow}Press Ctrl+C to stop all processes${colors.reset}`);
