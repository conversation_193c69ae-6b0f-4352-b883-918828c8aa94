import { supabaseAdmin } from "../supabase";

/**
 * Interface for usage data
 */
interface UsageData {
  userId: string;
  requestType: string;
  requestCount?: number;
  planId?: string;
}

/**
 * Interface for usage summary
 */
interface UsageSummary {
  daily: {
    total: number;
    limit: number;
    remaining: number;
    resetsAt: string; // ISO string of when the daily limit resets
  };
  monthly: {
    total: number;
    limit: number;
    remaining: number;
  };
  plan: {
    id: string;
    name: string;
    totalLimit: number;
    monthlyLimit: number;
    dailyLimit: number;
  };
}

/**
 * Record a usage request for a user
 * @param userId User ID
 * @param requestType Type of request (chat, search, etc.)
 * @param requestCount Number of requests to record (default: 1)
 * @returns Success status
 */
export async function recordUsage(
  userId: string,
  requestType: string,
  requestCount: number = 1,
  planId: string = 'free'
): Promise<{ success: boolean; error?: any }> {
  try {
    console.log(`Recording ${requestCount} ${requestType} request(s) for user ${userId} with plan ${planId}`);

    // Calculate billing period based on plan
    const now = new Date();
    let billingPeriodEnd: Date;

    if (planId === 'ultimate') {
      // Ultimate plan: 1 year from now
      billingPeriodEnd = new Date(now);
      billingPeriodEnd.setFullYear(now.getFullYear() + 1);
    } else {
      // Pro and Free plans: 1 month from now
      billingPeriodEnd = new Date(now);
      billingPeriodEnd.setMonth(now.getMonth() + 1);
    }

    // Store the usage in the database
    const { error } = await supabaseAdmin
      .from('usage_tracking')
      .insert({
        user_id: userId,
        request_type: requestType,
        request_count: requestCount,
        plan_id: planId,
        billing_period_start: now.toISOString(),
        billing_period_end: billingPeriodEnd.toISOString()
      });

    if (error) {
      console.error('Supabase error recording usage:', error);
      throw error;
    }

    console.log(`Successfully recorded usage for user ${userId}`);
    return { success: true };
  } catch (error) {
    console.error('Error recording usage:', error);
    return { success: false, error };
  }
}

/**
 * Get usage summary for a user
 * @param userId User ID
 * @returns Usage summary
 */
export async function getUserUsageSummary(userId: string): Promise<{
  success: boolean;
  summary?: UsageSummary;
  error?: any;
}> {
  try {
    console.log(`Getting usage summary for user ${userId}`);

    // Get user's plan
    const { data: subscriptionData, error: subscriptionError } = await supabaseAdmin
      .from('subscriptions')
      .select('plan_id, created_at')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    if (subscriptionError && subscriptionError.code !== 'PGRST116') {
      console.error('Error getting user subscription:', subscriptionError);
      throw subscriptionError;
    }

    const planId = subscriptionData?.plan_id || 'free';

    // Define plan limits - all plans have unlimited usage
    const planLimits = {
      free: { total: -1, monthly: -1, daily: -1, name: 'Premium' },
      pro: { total: -1, monthly: -1, daily: -1, name: 'Premium' },
      ultimate: { total: -1, monthly: -1, daily: -1, name: 'Premium' }
    };

    const plan = planLimits[planId as keyof typeof planLimits] || planLimits.free;

    // Get start of day, month for tracking
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);

    // Get total usage (for all plans)
    const { data: totalData, error: totalError } = await supabaseAdmin
      .from('usage_tracking')
      .select('request_count')
      .eq('user_id', userId);

    if (totalError) {
      console.error('Error getting total usage:', totalError);
      throw totalError;
    }

    // Get daily usage (primarily for free plan)
    const { data: dailyData, error: dailyError } = await supabaseAdmin
      .from('usage_tracking')
      .select('request_count')
      .eq('user_id', userId)
      .gte('timestamp', startOfDay.toISOString())
      .lt('timestamp', new Date(startOfDay.getTime() + 24 * 60 * 60 * 1000).toISOString());

    if (dailyError) {
      console.error('Error getting daily usage:', dailyError);
      throw dailyError;
    }

    // Get monthly usage (for pro/ultimate plans)
    const { data: monthlyData, error: monthlyError } = await supabaseAdmin
      .from('usage_tracking')
      .select('request_count')
      .eq('user_id', userId)
      .gte('timestamp', startOfMonth.toISOString())
      .lt('timestamp', new Date(startOfMonth.getFullYear(), startOfMonth.getMonth() + 1, 1).toISOString());

    if (monthlyError) {
      console.error('Error getting monthly usage:', monthlyError);
      throw monthlyError;
    }

    // Calculate totals
    const totalUsage = totalData?.reduce((sum, item) => sum + (item.request_count || 0), 0) || 0;
    const dailyTotal = dailyData?.reduce((sum, item) => sum + (item.request_count || 0), 0) || 0;
    const monthlyTotal = monthlyData?.reduce((sum, item) => sum + (item.request_count || 0), 0) || 0;

    // Calculate when the daily limit resets (midnight UTC)
    const tomorrow = new Date(startOfDay);
    tomorrow.setDate(tomorrow.getDate() + 1);
    const resetTime = tomorrow.toISOString();

    // Create summary based on plan type
    let summary: UsageSummary;

    if (planId === 'free') {
      // For free plan, show daily usage against the daily limit
      summary = {
        daily: {
          total: dailyTotal,
          limit: plan.daily,
          remaining: Math.max(0, plan.daily - dailyTotal),
          resetsAt: resetTime
        },
        monthly: {
          total: totalUsage,
          limit: plan.total,
          remaining: Math.max(0, plan.total - totalUsage)
        },
        plan: {
          id: planId,
          name: plan.name,
          totalLimit: plan.total,
          monthlyLimit: plan.monthly,
          dailyLimit: plan.daily
        }
      };
    } else {
      // For paid plans, show monthly usage
      summary = {
        daily: {
          total: dailyTotal,
          limit: plan.daily,
          remaining: Math.max(0, plan.daily - dailyTotal),
          resetsAt: resetTime
        },
        monthly: {
          total: monthlyTotal,
          limit: plan.monthly,
          remaining: Math.max(0, plan.monthly - monthlyTotal)
        },
        plan: {
          id: planId,
          name: plan.name,
          totalLimit: plan.total,
          monthlyLimit: plan.monthly,
          dailyLimit: plan.daily
        }
      };
    }

    return { success: true, summary };
  } catch (error) {
    console.error('Error getting user usage summary:', error);
    return { success: false, error };
  }
}

/**
 * Check if a user has exceeded their usage limit
 * @param userId User ID
 * @returns Whether the user has exceeded their limit
 */
export async function checkUsageLimit(userId: string): Promise<{
  success: boolean;
  hasReachedLimit: boolean;
  error?: any;
}> {
  // No usage limits for any users
  console.log(`No usage limits for user ${userId} - all features are free`);
  return { success: true, hasReachedLimit: false };
}
