import { Switch, Route } from "wouter";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { TooltipProvider } from "@/components/ui/tooltip";
import { ThemeProvider } from "@/components/ThemeProvider";
import { GlowCursor } from "@/components/ui/glow-cursor";
import { ToastProvider } from "@/components/ui/use-toast";
import NotFound from "@/pages/not-found";
import Home from "@/pages/Home";
import Features from "@/pages/Features";
import Integration from "@/pages/Integration";
import Plans from "@/pages/Plans";
import Contact from "@/pages/Contact";
import Documentation from "@/pages/Documentation";
import PrivacyPolicy from "@/pages/PrivacyPolicy";
import { Toaster } from "@/components/ui/toaster";

function Router() {
  return (
    <Switch>
      <Route path="/" component={Home} />
      <Route path="/features" component={Features} />
      <Route path="/integration" component={Integration} />
      <Route path="/plans" component={Plans} />
      <Route path="/contact" component={Contact} />

      <Route path="/privacy-policy" component={PrivacyPolicy} />
      <Route path="/documentation" component={Documentation} />
      {/* Fallback to 404 */}
      <Route component={NotFound} />
    </Switch>
  );
}

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider defaultTheme="light" storageKey="browzy-ai-theme">
        <div className="dark:bg-black light:bg-white min-h-screen">
          <GlowCursor
              color="rgba(0, 166, 192, 0.5)"
              size={40}
              blur={18}
              opacity={0.7}
              delay={30}
              pulseSpeed={2000}
            />
            <TooltipProvider>
              <ToastProvider>
                <Router />
              </ToastProvider>
            </TooltipProvider>
          </div>
      </ThemeProvider>
    </QueryClientProvider>
  );
}

export default App;
