# Server Backend for browzy

This is the backend server for the browzy application. It provides API endpoints for authentication, user management, and other functionality.

## Setup

1. Install dependencies:
```bash
npm install
```

2. Create a `.env` file with the following variables:
```
# Supabase credentials
SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_KEY=your_service_key_here

# Database connection
DATABASE_URL=your_database_url

# Server settings
PORT=5000
NODE_ENV=development
```

3. Start the server:
```bash
npm run dev
```

## API Endpoints

### Authentication

- `POST /api/auth/verify`: Verify a user's token
  - Request body: `{ token: string }`
  - Response: `{ message: string, user: object }`

### User Management

- `GET /api/user/profile`: Get the current user's profile (requires authentication)
  - Response: `{ profile: object }`

- `PUT /api/user/preferences`: Update user preferences (requires authentication)
  - Request body: `{ preferences: object }`
  - Response: `{ message: string, profile: object }`

### Contact Form

- `POST /api/contact`: Submit a contact form
  - Request body: `{ name: string, email: string, subject: string, message: string, consent: boolean }`
  - Response: `{ message: string, status: string }`

## Authentication

The server uses Supabase for authentication. To authenticate API requests, include an `Authorization` header with a Bearer token:

```
Authorization: Bearer your_token_here
```

## Integration with Frontend

The frontend can interact with the backend using the API client in `client/src/lib/api.ts`. This client handles authentication and provides methods for all API endpoints.

## Environment Variables

- `SUPABASE_URL`: Your Supabase project URL
- `SUPABASE_SERVICE_KEY`: Your Supabase service key (admin access)
- `DATABASE_URL`: PostgreSQL connection string
- `PORT`: Port for the server to listen on (default: 5000)
- `NODE_ENV`: Environment (development, production)
