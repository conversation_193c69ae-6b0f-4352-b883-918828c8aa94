import { ReactNode, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import clsx from 'clsx';
import { Button, ButtonProps } from './button';

interface FlamethrowerButtonProps extends ButtonProps {
  children: ReactNode;
  flameHeight?: number;
  flameWidth?: number;
  className?: string;
}

export function FlamethrowerButton({
  children,
  flameHeight = 100,
  flameWidth = 140,
  className,
  ...props
}: FlamethrowerButtonProps) {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <div 
      className={clsx("relative overflow-hidden", className)}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <Button {...props}>
        {children}
      </Button>

      <AnimatePresence>
        {isHovered && (
          <motion.div
            initial={{ opacity: 0, scaleY: 0.8 }}
            animate={{ opacity: 0.9, scaleY: 1.2 }}
            exit={{ opacity: 0, scaleY: 0.8 }}
            transition={{ duration: 0.3 }}
            className="absolute bottom-0 left-0 w-full"
            style={{
              height: flameHeight,
              width: flameWidth,
              left: '50%',
              transform: 'translateX(-50%)',
              bottom: -flameHeight + 20,
              zIndex: -1,
              transformOrigin: 'center bottom'
            }}
          >
            <div
              className="absolute w-full h-full"
              style={{
                background: 'linear-gradient(to top, #FF3864, #FFB627, rgba(255, 255, 255, 0.8))',
                clipPath: 'polygon(0% 100%, 5% 60%, 10% 80%, 15% 40%, 20% 70%, 25% 30%, 30% 50%, 35% 70%, 40% 30%, 45% 60%, 50% 20%, 55% 70%, 60% 40%, 65% 80%, 70% 30%, 75% 70%, 80% 40%, 85% 90%, 90% 50%, 95% 70%, 100% 100%)',
                filter: 'blur(5px)'
              }}
            />
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
