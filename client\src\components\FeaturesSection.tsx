import { motion } from 'framer-motion';
import { FEATURES } from '@/lib/constants';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Line<PERSON>hart,
  Flame,
  ShieldCheck,
  Zap
} from 'lucide-react';

const iconComponents = {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  LineChart,
  Flame,
  ShieldCheck,
  Zap
};

export default function FeaturesSection() {
  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0, transition: { duration: 0.5 } }
  };

  const getIconComponent = (iconName: string) => {
    const IconComponent = iconComponents[iconName as keyof typeof iconComponents];
    return IconComponent ? <IconComponent className="w-6 h-6" /> : null;
  };

  const getColorClass = (color: string) => {
    switch (color) {
      case 'primary': return 'text-primary';
      case 'secondary': return 'text-secondary';
      case 'accent': return 'text-secondary';
      default: return 'text-primary';
    }
  };

  const getGradientClass = (color: string) => {
    switch (color) {
      case 'primary': return 'from-primary/20 to-transparent';
      case 'secondary': return 'from-secondary/20 to-transparent';
      case 'accent': return 'from-secondary/20 to-primary/20';
      default: return 'from-primary/20 to-transparent';
    }
  };

  return (
    <section id="features" className="py-16 md:py-24 relative">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="text-center mb-20 relative"
      >
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[80%] h-[120%] -z-10">
          <div className="absolute inset-0 bg-gradient-to-r from-primary/5 via-accent/10 to-primary/5 rounded-full blur-3xl opacity-60"></div>
        </div>

        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.7, delay: 0.2 }}
          className="inline-block mb-3 px-5 py-2 bg-primary/10 rounded-full text-primary text-sm font-medium border border-primary/20"
        >
          Powerful Capabilities
        </motion.div>

        <h2 className="section-heading font-inter text-5xl md:text-6xl font-bold inline-block mx-auto tracking-tight mb-6">
          <span className="bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary relative inline-block">
            Features
            <span className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-primary/50 to-secondary/50 rounded-full"></span>
          </span>
          <span className="ml-3">That Actually Work</span>
        </h2>

        <p className="text-muted-foreground mt-6 max-w-2xl mx-auto text-lg md:text-xl font-inter leading-relaxed">
          Unlike your dating profile, these features actually deliver what they promise.
          <span className="block mt-2 text-secondary italic">No false advertising here.</span>
        </p>
      </motion.div>

      <div className="relative">
        {/* Background elements */}
        <div className="absolute top-1/4 left-1/4 w-1/3 h-1/3 bg-primary/5 rounded-full blur-3xl -z-10"></div>
        <div className="absolute bottom-1/4 right-1/4 w-1/3 h-1/3 bg-accent/5 rounded-full blur-3xl -z-10"></div>

        <motion.div
          className="grid md:grid-cols-3 gap-10"
          variants={container}
          initial="hidden"
          whileInView="show"
          viewport={{ once: true, amount: 0.2 }}
        >
          {FEATURES.map((feature) => (
            <motion.div key={feature.id} variants={item}>
              <div className="h-full flex flex-col transition-all duration-500 overflow-hidden group relative">
                {/* Card background with hover effect */}
                <div className="absolute inset-0 bg-card rounded-2xl border border-muted/50 shadow-lg group-hover:shadow-xl transition-all duration-500 group-hover:border-primary/30"></div>
                <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-accent/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-2xl"></div>

                {/* Card content */}
                <div className="relative p-6 md:p-8 flex flex-col h-full z-10">
                  {/* Icon with animated background */}
                  <div className="relative mb-6">
                    <div className={`absolute inset-0 ${getGradientClass(feature.color)} rounded-xl blur-sm opacity-70 group-hover:opacity-100 transition-opacity duration-500`}></div>
                    <div className={`w-16 h-16 rounded-xl flex items-center justify-center relative bg-gradient-to-br ${getGradientClass(feature.color)} shadow-lg group-hover:shadow-xl transition-all duration-500 transform group-hover:scale-105`}>
                      <div className={`${getColorClass(feature.color)}`}>
                        {getIconComponent(feature.icon)}
                      </div>
                    </div>
                  </div>

                  {/* Title with underline effect */}
                  <h3 className="font-inter text-2xl font-semibold mb-4 group-hover:text-primary transition-colors duration-300 tracking-tight relative inline-block">
                    {feature.title}
                    <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-primary group-hover:w-full transition-all duration-500"></span>
                  </h3>

                  {/* Description */}
                  <p className="text-muted-foreground flex-grow mb-5 font-inter text-base leading-relaxed">
                    {feature.description}
                  </p>

                  {/* Command text with improved styling */}
                  <div className="py-3 px-4 bg-background/80 backdrop-blur-sm rounded-lg text-primary font-inter text-sm inline-flex items-center border border-muted/50 group-hover:border-primary/30 transition-all duration-500 shadow-sm">
                    <span className="mr-2 opacity-70 text-secondary">&gt;</span>
                    {feature.commandText}
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>
      </div>

      <div className="my-24 relative">
        <div className="h-px w-full bg-gradient-to-r from-transparent via-primary/40 to-transparent"></div>

        <div className="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 w-20 h-20">
          <div className="absolute inset-0 bg-gradient-to-r from-primary/30 to-accent/30 rounded-full blur-xl animate-pulse-slow"></div>
          <div className="absolute inset-3 bg-gradient-to-r from-primary/20 to-accent/20 rounded-full blur-md animate-pulse-slow-reverse"></div>
        </div>

        <div className="absolute left-1/4 top-1/2 transform -translate-x-1/2 -translate-y-1/2 w-10 h-10">
          <div className="absolute inset-0 bg-secondary/20 rounded-full blur-lg animate-pulse-slow-reverse"></div>
        </div>

        <div className="absolute right-1/4 top-1/2 transform translate-x-1/2 -translate-y-1/2 w-10 h-10">
          <div className="absolute inset-0 bg-accent/20 rounded-full blur-lg animate-pulse-slow"></div>
        </div>
      </div>
    </section>
  );
}
