-- Create usage_records table
CREATE TABLE IF NOT EXISTS usage_records (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  service VARCHAR(50) NOT NULL,
  model VARCHAR(100),
  request_type VARCHAR(50) NOT NULL,
  tokens INT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  metadata JSONB
);

-- Create index on user_id for faster lookups
CREATE INDEX IF NOT EXISTS idx_usage_records_user_id ON usage_records(user_id);

-- Create index on created_at for time-based queries
CREATE INDEX IF NOT EXISTS idx_usage_records_created_at ON usage_records(created_at);

-- Create index on service for filtering by service
CREATE INDEX IF NOT EXISTS idx_usage_records_service ON usage_records(service);

-- Grant permissions to authenticated users
ALTER TABLE usage_records ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only see their own usage records
CREATE POLICY select_own_usage_records ON usage_records
  FOR SELECT USING (auth.uid() = user_id);

-- Policy: Users can only insert their own usage records
CREATE POLICY insert_own_usage_records ON usage_records
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Policy: Service role can do anything
CREATE POLICY service_role_all ON usage_records
  USING (auth.role() = 'service_role');

-- Grant permissions to anon and authenticated roles
GRANT SELECT, INSERT ON usage_records TO anon, authenticated;
GRANT USAGE, SELECT ON SEQUENCE usage_records_id_seq TO anon, authenticated;
