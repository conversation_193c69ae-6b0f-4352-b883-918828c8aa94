// Script to check if the extension_tokens table exists
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client with service role key
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY;

console.log('Supabase URL:', supabaseUrl);
console.log('Supabase Service Key:', supabaseServiceKey ? 'Set correctly' : 'Missing');

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase URL or Service Key. Please check your environment variables.');
  process.exit(1);
}

// Create Supabase client
const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function checkExtensionTokensTable() {
  try {
    console.log('Checking if extension_tokens table exists...');

    // Try to select from the table to see if it exists
    const { data, error } = await supabaseAdmin
      .from('extension_tokens')
      .select('id')
      .limit(1);

    if (error) {
      if (error.code === '42P01') { // Table doesn't exist
        console.log('Table extension_tokens does not exist!');
        return false;
      }

      console.error('Error checking if table exists:', error);
      return false;
    }

    console.log('Successfully queried extension_tokens table');
    return true;
  } catch (error) {
    console.error('Error checking extension_tokens table:', error);
    return false;
  }
}

// Run the function
checkExtensionTokensTable()
  .then(exists => {
    if (exists) {
      console.log('The extension_tokens table exists in the database.');
    } else {
      console.log('The extension_tokens table does not exist in the database.');
      console.log('Please run the SQL in create_extension_tokens.sql in the Supabase dashboard SQL editor.');
    }
    process.exit(0);
  })
  .catch(error => {
    console.error('Script failed:', error);
    process.exit(1);
  });
