{"name": "browzy-server", "version": "1.0.0", "description": "Backend server for browzy application", "main": "index.js", "scripts": {"start": "ts-node index.ts", "dev": "nodemon --exec ts-node index.ts", "build": "tsc"}, "dependencies": {"@supabase/supabase-js": "^2.49.4", "axios": "^1.9.0", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.18.2", "helmet": "^7.1.0", "pg": "^8.15.6"}, "devDependencies": {"@types/cookie-parser": "^1.4.8", "@types/cors": "^2.8.16", "@types/express": "^4.17.21", "@types/node": "^20.9.0", "nodemon": "^3.0.1", "ts-node": "^10.9.1", "typescript": "^5.2.2"}}