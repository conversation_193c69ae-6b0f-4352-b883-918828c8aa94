import { useEffect } from 'react';
import { motion } from 'framer-motion';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { PageHeader } from '@/components/PageHeader';

export default function PrivacyPolicy() {
  useEffect(() => {
    // Update document title
    document.title = "Privacy Policy - browzy - Supercharge Your Browser with AI";

    // Add meta description
    const metaDescription = document.createElement('meta');
    metaDescription.name = 'description';
    metaDescription.content = 'Browzy AI Privacy Policy - Learn how we protect your data and privacy when using our browser extension.';
    document.head.appendChild(metaDescription);

    window.scrollTo(0, 0);

    return () => {
      document.head.removeChild(metaDescription);
    };
  }, []);

  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0, transition: { duration: 0.5 } }
  };

  return (
    <div className="min-h-screen dark:bg-black light:bg-white text-foreground overflow-hidden">
      <Header />
      
      <PageHeader 
        title="Privacy Policy" 
        description="Last Updated: May 15, 2025" 
      />

      <div className="max-w-4xl mx-auto px-6 sm:px-8 lg:px-12 py-12">
        <motion.div
          variants={container}
          initial="hidden"
          animate="show"
          className="prose dark:prose-invert prose-lg max-w-none font-inter"
        >
          <motion.section variants={item} className="mb-10">
            <h2 className="text-2xl font-bold mb-4">Introduction</h2>
            <p className="text-muted-foreground">
              At Browzy AI, we are committed to protecting your privacy and being transparent about how we use your data. 
              This Privacy Policy explains what information we collect, how we use it, and the choices you have regarding your data.
            </p>
          </motion.section>

          <motion.section variants={item} className="mb-10">
            <h2 className="text-2xl font-bold mb-4">1. Information We Collect</h2>
            
            <h3 className="text-xl font-semibold mb-3">1.1 Information You Provide</h3>
            <ul className="list-disc pl-6 mb-4 text-muted-foreground">
              <li>Chat messages and queries you submit to the AI assistant</li>
              <li>API keys you enter for third-party services (such as OpenRouter)</li>
              <li>Preferences and settings you configure within the extension</li>
            </ul>

            <h3 className="text-xl font-semibold mb-3">1.2 Information Collected Automatically</h3>
            <ul className="list-disc pl-6 mb-4 text-muted-foreground">
              <li>Basic device information (browser type, operating system)</li>
              <li>Extension usage statistics (features used, number of requests)</li>
              <li>Content from web pages you choose to analyze with our extension features</li>
              <li>Error and performance data to improve the extension</li>
            </ul>

            <h3 className="text-xl font-semibold mb-3">1.3 Web Page Content</h3>
            <p className="text-muted-foreground mb-2">
              When you use certain features of our extension (such as Web MCP or Smart Search), we may collect and process content from the web pages you're visiting to provide the requested functionality. This content is:
            </p>
            <ul className="list-disc pl-6 text-muted-foreground">
              <li>Only collected when you explicitly activate these features</li>
              <li>Processed locally within your browser when possible</li>
              <li>Transmitted to AI services (like OpenRouter) when necessary to generate responses</li>
              <li>Not permanently stored on our servers</li>
            </ul>
          </motion.section>

          <motion.section variants={item} className="mb-10">
            <h2 className="text-2xl font-bold mb-4">2. How We Use Your Information</h2>
            <p className="text-muted-foreground mb-2">We use the information we collect to:</p>
            <ul className="list-disc pl-6 text-muted-foreground">
              <li>Provide, maintain, and improve the Browzy AI extension</li>
              <li>Process and complete your requests and generate AI responses</li>
              <li>Send you technical notices, updates, and support messages</li>
              <li>Monitor and analyze usage patterns to enhance the user experience</li>
              <li>Detect, investigate, and prevent fraudulent or unauthorized use</li>
              <li>Comply with legal obligations</li>
            </ul>
          </motion.section>

          <motion.section variants={item} className="mb-10">
            <h2 className="text-2xl font-bold mb-4">3. Data Sharing and Disclosure</h2>
            
            <h3 className="text-xl font-semibold mb-3">3.1 Third-Party Service Providers</h3>
            <p className="text-muted-foreground mb-2">
              We use third-party services to help us operate our extension:
            </p>
            <ul className="list-disc pl-6 mb-4 text-muted-foreground">
              <li><strong>OpenRouter:</strong> Processes your queries to generate AI responses</li>
              <li><strong>Browser Storage:</strong> Stores your preferences and settings locally</li>
            </ul>
            <p className="text-muted-foreground">
              When you use our extension, your queries may be sent to these services in accordance with their respective privacy policies.
            </p>

            <h3 className="text-xl font-semibold mb-3 mt-6">3.2 Legal Requirements</h3>
            <p className="text-muted-foreground">
              We will never disclose your any information so browzy ai safe and take care of user data 
            </p>

            <h3 className="text-xl font-semibold mb-3 mt-6">3.3 Business Transfers</h3>
            <p className="text-muted-foreground">
              If we are involved in a merger, acquisition, or sale of assets, the user data will never be transferred to the any new entity.
            </p>
          </motion.section>

          <motion.section variants={item} className="mb-10">
            <h2 className="text-2xl font-bold mb-4">4. Data Storage and Security</h2>
            <p className="text-muted-foreground mb-4">
              We implement appropriate technical and organizational measures to protect your personal information:
            </p>
            <ul className="list-disc pl-6 text-muted-foreground">
              <li>Most of your data is stored locally in your browser using secure browser storage</li>
              <li>API keys are stored securely and only used to authenticate with the respective services</li>
              <li>We use industry-standard security measures to protect any data transmitted to our servers</li>
            </ul>
            <p className="text-muted-foreground mt-4">
              While we strive to use commercially acceptable means to protect your personal information, we cannot guarantee its absolute security.
            </p>
          </motion.section>

          <motion.section variants={item} className="mb-10">
            <h2 className="text-2xl font-bold mb-4">5. Your Rights and Choices</h2>
            <p className="text-muted-foreground mb-4">
              You have several rights regarding your personal information:
            </p>
            <ul className="list-disc pl-6 text-muted-foreground">
              <li><strong>Access and Update:</strong> You can access and update your preferences through the extension settings</li>
              <li><strong>Delete:</strong> You can clear your chat history and stored data through the extension</li>
              <li><strong>Opt-Out:</strong> You can disable specific features that collect data through the settings</li>
              <li><strong>Uninstall:</strong> You can uninstall the extension at any time to stop all data collection</li>
            </ul>
          </motion.section>

          <motion.section variants={item} className="mb-10">
            <h2 className="text-2xl font-bold mb-4">6. Children's Privacy</h2>
            <p className="text-muted-foreground">
              Our extension is not intended for children under 13 years of age. We do not knowingly collect personal information from children under 13. 
              If you are a parent or guardian and believe your child has provided us with personal information, please contact us.
            </p>
          </motion.section>

          <motion.section variants={item} className="mb-10">
            <h2 className="text-2xl font-bold mb-4">7. Changes to This Privacy Policy</h2>
            <p className="text-muted-foreground">
              We may update this Privacy Policy from time to time. We will notify you of any changes by posting the new Privacy Policy on this page and updating the "Last Updated" date.
            </p>
          </motion.section>

          <motion.section variants={item} className="mb-10">
            <h2 className="text-2xl font-bold mb-4">8. Contact Us</h2>
            <p className="text-muted-foreground">
              If you have any questions about this Privacy Policy or our data practices, please contact us at:
            </p>
            <p className="mt-4">
              <strong>Email:</strong> <a href="mailto:<EMAIL>" className="text-primary hover:underline"><EMAIL></a>
            </p>
          </motion.section>
        </motion.div>
      </div>

      <Footer />
    </div>
  );
}
