-- Create user_connections table to track connection history
CREATE TABLE IF NOT EXISTS user_connections (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  token_id UUID REFERENCES extension_tokens(id) ON DELETE SET NULL,
  connection_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  extension_version VARCHAR(50),
  browser_name VARCHAR(100),
  browser_version VARCHAR(50),
  os_name VARCHAR(100),
  os_version VARCHAR(50),
  device_type VARCHAR(50),
  ip_address VARCHAR(50),
  user_agent TEXT,
  is_successful BOOLEAN DEFAULT TRUE,
  disconnect_time TIMESTAMP WITH TIME ZONE,
  connection_duration INTEGER -- in seconds
);

-- Create index on user_id for faster lookups
CREATE INDEX IF NOT EXISTS idx_user_connections_user_id ON user_connections(user_id);

-- <PERSON>reate index on connection_time for sorting
CREATE INDEX IF NOT EXISTS idx_user_connections_time ON user_connections(connection_time);

-- Create user_metrics table to store aggregated user metrics
CREATE TABLE IF NOT EXISTS user_metrics (
  user_id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  first_connection_time TIMESTAMP WITH TIME ZONE,
  last_connection_time TIMESTAMP WITH TIME ZONE,
  total_connections INTEGER DEFAULT 0,
  total_chat_messages INTEGER DEFAULT 0,
  total_connection_duration INTEGER DEFAULT 0, -- in seconds
  last_active_time TIMESTAMP WITH TIME ZONE,
  favorite_browser VARCHAR(100),
  favorite_device_type VARCHAR(50),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on user_connections
ALTER TABLE user_connections ENABLE ROW LEVEL SECURITY;

-- Create policy to allow users to see only their own connections
CREATE POLICY select_own_connections ON user_connections
  FOR SELECT USING (auth.uid() = user_id);

-- Create policy to allow users to insert their own connections
CREATE POLICY insert_own_connections ON user_connections
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Create policy to allow users to update their own connections
CREATE POLICY update_own_connections ON user_connections
  FOR UPDATE USING (auth.uid() = user_id);

-- Enable RLS on user_metrics
ALTER TABLE user_metrics ENABLE ROW LEVEL SECURITY;

-- Create policy to allow users to see only their own metrics
CREATE POLICY select_own_metrics ON user_metrics
  FOR SELECT USING (auth.uid() = user_id);

-- Create function to update user metrics when a new connection is made
CREATE OR REPLACE FUNCTION update_user_metrics_on_connection()
RETURNS TRIGGER AS $$
BEGIN
  -- Insert or update user metrics
  INSERT INTO user_metrics (
    user_id, 
    first_connection_time, 
    last_connection_time, 
    total_connections,
    last_active_time
  ) 
  VALUES (
    NEW.user_id, 
    NEW.connection_time, 
    NEW.connection_time, 
    1,
    NEW.connection_time
  )
  ON CONFLICT (user_id) 
  DO UPDATE SET
    last_connection_time = NEW.connection_time,
    total_connections = user_metrics.total_connections + 1,
    last_active_time = NEW.connection_time,
    updated_at = NOW();
    
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to update user metrics on new connection
CREATE TRIGGER update_user_metrics_trigger
AFTER INSERT ON user_connections
FOR EACH ROW
EXECUTE FUNCTION update_user_metrics_on_connection();
