import express, { type Express } from "express";
import fs from "fs";
import path from "path";
import { createServer as createViteServer, createLogger } from "vite";
import { type Server } from "http";
import viteConfig from "../vite.config";
import { nanoid } from "nanoid";
import { fileURLToPath } from "url";

// Fix for import.meta.dirname
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const viteLogger = createLogger();

export function log(message: string, source = "express") {
  const formattedTime = new Date().toLocaleTimeString("en-US", {
    hour: "numeric",
    minute: "2-digit",
    second: "2-digit",
    hour12: true,
  });

  console.log(`${formattedTime} [${source}] ${message}`);
}

export async function setupVite(app: Express, server: Server) {
  try {
    console.log('Setting up Vite...');

    const serverOptions = {
      middlewareMode: true,
      hmr: { server },
      allowedHosts: ['*'],
    };

    const vite = await createViteServer({
      ...viteConfig,
      configFile: false,
      customLogger: {
        ...viteLogger,
        error: (msg, options) => {
          viteLogger.error(msg, options);
          console.error('Vite server error:', msg);
        },
      },
      server: serverOptions,
      appType: "custom",
    });

    app.use(vite.middlewares);

    // Simplified route handler that doesn't use path.resolve with potentially undefined arguments
    app.use("*", async (req, res, next) => {
      try {
        // Just serve a static HTML page instead of trying to load the client template
        serveStaticHtml(res);
      } catch (e) {
        console.error('Error in Vite middleware:', e);
        next(e);
      }
    });
  } catch (error) {
    console.error('Error setting up Vite:', error);
    // Instead of throwing, just log the error and continue
    app.use("*", (req, res) => {
      serveStaticHtml(res);
    });
  }
}

export function serveStatic(app: Express) {
  console.log('In serveStatic function');

  // Create a public directory if it doesn't exist
  const publicDir = path.join(__dirname, '..', 'public');
  if (!fs.existsSync(publicDir)) {
    try {
      fs.mkdirSync(publicDir, { recursive: true });
      console.log(`Created public directory at: ${publicDir}`);

      // Create a simple index.html file
      const indexPath = path.join(publicDir, 'index.html');
      fs.writeFileSync(indexPath, `
        <!DOCTYPE html>
        <html lang="en">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>browzy</title>
          <style>
            body {
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
              max-width: 800px;
              margin: 0 auto;
              padding: 2rem;
              line-height: 1.6;
            }
            h1 {
              color: #0070f3;
            }
          </style>
        </head>
        <body>
          <h1>browzy</h1>
          <p>The application is running in production mode.</p>
        </body>
        </html>
      `);
      console.log(`Created index.html at: ${indexPath}`);
    } catch (error) {
      console.error('Error creating public directory:', error);
    }
  }

  // Serve static files from the public directory
  app.use(express.static(publicDir));

  // Fall through to index.html for any other routes
  app.use("*", (req, res) => {
    try {
      const indexPath = path.join(publicDir, 'index.html');
      if (fs.existsSync(indexPath)) {
        res.sendFile(indexPath);
      } else {
        serveStaticHtml(res);
      }
    } catch (error) {
      console.error('Error serving index.html:', error);
      serveStaticHtml(res);
    }
  });
}

// Helper function to serve a static HTML response
function serveStaticHtml(res: express.Response) {
  res.setHeader('Content-Type', 'text/html');
  res.status(200).send(`
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>BrowzyAI</title>
      <style>
        body {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
          max-width: 800px;
          margin: 0 auto;
          padding: 2rem;
          line-height: 1.6;
          color: #333;
          background-color: #f9f9f9;
        }
        h1 {
          color: #00b4d8;
        }
        .container {
          background-color: white;
          border-radius: 8px;
          padding: 2rem;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .status {
          display: inline-block;
          background-color: #e6f7f7;
          color: #00b4d8;
          padding: 0.5rem 1rem;
          border-radius: 4px;
          font-weight: bold;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <h1>BrowzyAI Server</h1>
        <p>The server is running and ready to handle requests.</p>
        <p><span class="status">API Status: Online</span></p>
        <p>If you're seeing this page, it means the server is working correctly but you're not accessing it through the frontend application.</p>
      </div>
    </body>
    </html>
  `);
}