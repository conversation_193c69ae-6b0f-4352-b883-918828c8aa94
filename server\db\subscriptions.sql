-- Create subscriptions table
CREATE TABLE IF NOT EXISTS subscriptions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    plan_id VARCHAR(50) NOT NULL,
    stripe_subscription_id VARCHAR(255),
    stripe_customer_id VARCHAR(255),
    status VARCHAR(50) NOT NULL DEFAULT 'active',
    current_period_start TIMESTAMP WITH TIME ZONE,
    current_period_end TIMESTAMP WITH TIME ZONE,
    cancel_at_period_end BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT unique_user_subscription UNIQUE (user_id)
);

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_subscriptions_user_id ON subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_status ON subscriptions(status);
CREATE INDEX IF NOT EXISTS idx_subscriptions_period_end ON subscriptions(current_period_end);

-- <PERSON>reate function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update the updated_at column
CREATE TRIGGER update_subscriptions_updated_at
BEFORE UPDATE ON subscriptions
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Create a function to check for expired subscriptions and update their status
CREATE OR REPLACE FUNCTION check_expired_subscriptions()
RETURNS void AS $$
BEGIN
    -- Update status to 'expired' for subscriptions that have passed their end date
    UPDATE subscriptions
    SET status = 'expired'
    WHERE status = 'active'
    AND current_period_end < NOW()
    AND cancel_at_period_end = TRUE;
    
    -- For subscriptions that are expired but not set to cancel, they should be renewed
    -- This would typically be handled by Stripe webhooks, but we'll add this as a fallback
END;
$$ LANGUAGE plpgsql;

-- Create a function to downgrade a user to the free plan
CREATE OR REPLACE FUNCTION downgrade_to_free_plan(user_uuid UUID)
RETURNS void AS $$
BEGIN
    -- Update the subscription to the free plan
    UPDATE subscriptions
    SET 
        plan_id = 'free',
        status = 'active',
        stripe_subscription_id = NULL,
        current_period_start = NOW(),
        current_period_end = NULL,
        cancel_at_period_end = FALSE,
        updated_at = NOW()
    WHERE user_id = user_uuid;
END;
$$ LANGUAGE plpgsql;

-- Create a function to set a user's subscription
CREATE OR REPLACE FUNCTION set_user_subscription(
    user_uuid UUID,
    plan VARCHAR(50),
    stripe_sub_id VARCHAR(255),
    stripe_cust_id VARCHAR(255),
    period_start TIMESTAMP WITH TIME ZONE,
    period_end TIMESTAMP WITH TIME ZONE,
    cancel_at_end BOOLEAN DEFAULT FALSE
)
RETURNS void AS $$
BEGIN
    -- Insert or update the subscription
    INSERT INTO subscriptions (
        user_id,
        plan_id,
        stripe_subscription_id,
        stripe_customer_id,
        status,
        current_period_start,
        current_period_end,
        cancel_at_period_end
    ) VALUES (
        user_uuid,
        plan,
        stripe_sub_id,
        stripe_cust_id,
        'active',
        period_start,
        period_end,
        cancel_at_end
    )
    ON CONFLICT (user_id) 
    DO UPDATE SET
        plan_id = plan,
        stripe_subscription_id = stripe_sub_id,
        stripe_customer_id = stripe_cust_id,
        status = 'active',
        current_period_start = period_start,
        current_period_end = period_end,
        cancel_at_period_end = cancel_at_end,
        updated_at = NOW();
END;
$$ LANGUAGE plpgsql;
