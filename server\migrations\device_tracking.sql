-- Create device_fingerprints table
CREATE TABLE IF NOT EXISTS device_fingerprints (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  fingerprint VARCHAR(255) NOT NULL,
  ip_address VARCHAR(50),
  first_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  request_count INTEGER DEFAULT 0,
  UNIQUE(user_id, fingerprint)
);

-- <PERSON>reate indexes
CREATE INDEX IF NOT EXISTS idx_device_fingerprints_fingerprint ON device_fingerprints(fingerprint);
CREATE INDEX IF NOT EXISTS idx_device_fingerprints_user_id ON device_fingerprints(user_id);
CREATE INDEX IF NOT EXISTS idx_device_fingerprints_ip ON device_fingerprints(ip_address);

-- Create suspicious_activity table to track potential abuse
CREATE TABLE IF NOT EXISTS suspicious_activity (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  device_fingerprint VARCHAR(255),
  ip_address VARCHAR(50),
  activity_type VARCHAR(50) NOT NULL, -- 'multiple_accounts', 'rapid_requests', etc.
  details JSONB,
  timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index on suspicious_activity
CREATE INDEX IF NOT EXISTS idx_suspicious_activity_user_id ON suspicious_activity(user_id);
CREATE INDEX IF NOT EXISTS idx_suspicious_activity_fingerprint ON suspicious_activity(device_fingerprint);
CREATE INDEX IF NOT EXISTS idx_suspicious_activity_ip ON suspicious_activity(ip_address);
CREATE INDEX IF NOT EXISTS idx_suspicious_activity_type ON suspicious_activity(activity_type);

-- Create ip_usage_tracking table
CREATE TABLE IF NOT EXISTS ip_usage_tracking (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  ip_address VARCHAR(50) NOT NULL,
  request_count INTEGER DEFAULT 0,
  account_count INTEGER DEFAULT 0,
  first_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(ip_address)
);

-- Create index on ip_usage_tracking
CREATE INDEX IF NOT EXISTS idx_ip_usage_tracking_ip ON ip_usage_tracking(ip_address);

-- Create RLS policies

-- Enable RLS on device_fingerprints
ALTER TABLE device_fingerprints ENABLE ROW LEVEL SECURITY;

-- Create policy to allow users to see only their own device fingerprints
CREATE POLICY select_own_device_fingerprints ON device_fingerprints
  FOR SELECT USING (auth.uid() = user_id);

-- Enable RLS on suspicious_activity
ALTER TABLE suspicious_activity ENABLE ROW LEVEL SECURITY;

-- Create policy to allow users to see only their own suspicious activity
CREATE POLICY select_own_suspicious_activity ON suspicious_activity
  FOR SELECT USING (auth.uid() = user_id);

-- Enable RLS on ip_usage_tracking (admin only)
ALTER TABLE ip_usage_tracking ENABLE ROW LEVEL SECURITY;
