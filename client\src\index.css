/*
 * Note: Styrene by <PERSON><PERSON> and <PERSON><PERSON><PERSON>s by Klim Type Foundry are premium fonts.
 * You'll need to purchase and include these fonts using one of the following methods:
 * 1. Self-host the font files and use @font-face
 * 2. Use a font service like Adobe Fonts
 * 3. Include them via a CDN if available
 *
 * Example for self-hosting:
 * @font-face {
 *   font-family: 'Styrene A';
 *   src: url('/fonts/StyreneA-Regular.woff2') format('woff2'),
 *        url('/fonts/StyreneA-Regular.woff') format('woff');
 *   font-weight: 400;
 *   font-style: normal;
 *   font-display: swap;
 * }
 */

/* Import fonts */
@import url('https://fonts.googleapis.com/css2?family=Roboto+Mono:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --radius: 0.5rem;
}

.dark {
  /* Dark mode color scheme - Turquoise color palette with black background */
  --background: 0 0% 0%;          /* Pure Black: #000000 */
  --foreground: 0 0% 95%;         /* White / Cream: #F3F3F3 */
  --muted: 210 20% 10%;           /* Very Dark Navy/Teal: #131820 */
  --muted-foreground: 0 0% 80%;   /* Light Gray: #CCCCCC */
  --popover: 210 20% 8%;          /* Almost Black Navy/Teal: #0f141a */
  --popover-foreground: 0 0% 95%; /* White / Cream: #F3F3F3 */
  --card: 0 0% 0%;                /* Pure Black: #000000 */
  --card-foreground: 0 0% 95%;    /* White / Cream: #F3F3F3 */
  --border: 187 60% 20%;          /* Subtle Turquoise Border: #0d4a54 */
  --input: 210 20% 10%;           /* Very Dark Navy/Teal: #131820 */
  --primary: 187 100% 38%;        /* Bright Turquoise: #00a6c0 */
  --primary-foreground: 0 0% 100%;
  --secondary: 204 28% 22%;       /* Medium Teal/Blue: #283b48 */
  --secondary-foreground: 0 0% 95%; /* White / Cream: #F3F3F3 */
  --accent: 180 100% 57%;         /* Light Turquoise: #48d7ce */
  --accent-foreground: 210 20% 16%; /* Dark Navy/Teal: #222831 */
  --destructive: 0 84% 60%;       /* Bright Red: #F24C4C */
  --destructive-foreground: 0 0% 98%;
  --ring: 187 100% 38%;           /* Bright Turquoise: #00a6c0 */

  /* Sidebar colors */
  --sidebar-background: 0 0% 0%;      /* Pure Black: #000000 */
  --sidebar-foreground: 0 0% 95%;     /* White / Cream: #F3F3F3 */
  --sidebar-primary: 187 100% 38%;    /* Bright Turquoise: #00a6c0 */
  --sidebar-primary-foreground: 0 0% 100%;
  --sidebar-accent: 210 20% 10%;      /* Very Dark Navy/Teal: #131820 */
  --sidebar-accent-foreground: 0 0% 95%; /* White / Cream: #F3F3F3 */
  --sidebar-border: 210 20% 15%;      /* Dark Navy/Teal Border: #1c2530 */
  --sidebar-ring: 187 100% 38%;       /* Bright Turquoise: #00a6c0 */

  /* Chart colors */
  --chart-1: 187 100% 38%;         /* Bright Turquoise: #00a6c0 */
  --chart-2: 180 100% 57%;         /* Light Turquoise: #48d7ce */
  --chart-3: 204 28% 22%;          /* Medium Teal/Blue: #283b48 */
  --chart-4: 210 20% 16%;          /* Dark Navy/Teal: #222831 */
  --chart-5: 0 0% 95%;             /* White / Cream: #F3F3F3 */
}

.light {
  /* Light mode color scheme - Turquoise color palette */
  --background: 0 0% 98%;         /* Off-White: #fafafa */
  --foreground: 210 20% 16%;      /* Dark Navy/Teal: #222831 */
  --muted: 180 20% 92%;           /* Very Light Turquoise: #eaf7f7 */
  --muted-foreground: 204 28% 30%; /* Medium Teal/Blue (darker): #3a5369 */
  --popover: 0 0% 98%;            /* Off-White: #fafafa */
  --popover-foreground: 210 20% 16%; /* Dark Navy/Teal: #222831 */
  --card: 0 0% 100%;              /* Pure White: #FFFFFF */
  --card-foreground: 210 20% 16%; /* Dark Navy/Teal: #222831 */
  --border: 180 15% 85%;          /* Light Teal Border: #d5e3e3 */
  --input: 180 20% 92%;           /* Very Light Turquoise: #eaf7f7 */
  --primary: 187 100% 38%;        /* Bright Turquoise: #00a6c0 */
  --primary-foreground: 0 0% 100%;
  --secondary: 204 28% 22%;       /* Medium Teal/Blue: #283b48 */
  --secondary-foreground: 0 0% 98%; /* Off-White: #fafafa */
  --accent: 180 100% 57%;         /* Light Turquoise: #48d7ce */
  --accent-foreground: 210 20% 16%; /* Dark Navy/Teal: #222831 */
  --destructive: 0 84% 60%;       /* Bright Red: #F24C4C */
  --destructive-foreground: 0 0% 100%;
  --ring: 187 100% 38%;           /* Bright Turquoise: #00a6c0 */

  /* Sidebar colors */
  --sidebar-background: 0 0% 98%; /* Off-White: #fafafa */
  --sidebar-foreground: 210 20% 16%; /* Dark Navy/Teal: #222831 */
  --sidebar-primary: 187 100% 38%;   /* Bright Turquoise: #00a6c0 */
  --sidebar-primary-foreground: 0 0% 100%;
  --sidebar-accent: 180 20% 92%;     /* Very Light Turquoise: #eaf7f7 */
  --sidebar-accent-foreground: 210 20% 16%; /* Dark Navy/Teal: #222831 */
  --sidebar-border: 180 15% 85%;     /* Light Teal Border: #d5e3e3 */
  --sidebar-ring: 187 100% 38%;      /* Bright Turquoise: #00a6c0 */

  /* Chart colors */
  --chart-1: 187 100% 38%;         /* Bright Turquoise: #00a6c0 */
  --chart-2: 180 100% 57%;         /* Light Turquoise: #48d7ce */
  --chart-3: 204 28% 22%;          /* Medium Teal/Blue: #283b48 */
  --chart-4: 210 20% 16%;          /* Dark Navy/Teal: #222831 */
  --chart-5: 0 0% 98%;             /* Off-White: #fafafa */
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased text-foreground;
    overflow-x: hidden;
  }

  .dark body {
    background-color: black;
  }

  .light body {
    background-color: white;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-heading;
  }

  /* Modern clean UI styles */
  .modern-card {
    padding: 1.75rem;
    border-radius: 1rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
  }

  /* Dark mode card styling */
  .dark .modern-card {
    background: black;
    border: 1px solid rgba(0, 166, 192, 0.2);
    box-shadow: 0 0 15px rgba(0, 166, 192, 0.3),
                0 0 30px rgba(0, 166, 192, 0.1),
                inset 0 1px 0 0 rgba(255, 255, 255, 0.05);
  }

  /* Light mode card styling */
  .light .modern-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(240, 240, 240, 0.8));
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    border: 1px solid rgba(0, 166, 192, 0.1);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08),
                inset 0 1px 0 0 rgba(255, 255, 255, 0.8);
  }

  .modern-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, transparent, hsl(var(--primary)/0.7), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .modern-card:hover {
    transform: translateY(-3px);
  }

  /* Dark mode card hover */
  .dark .modern-card:hover {
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.35),
                0 0 15px rgba(0, 166, 192, 0.2);
    border-color: rgba(0, 166, 192, 0.4);
  }

  /* Light mode card hover */
  .light .modern-card:hover {
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1),
                0 0 15px rgba(0, 166, 192, 0.15);
    border-color: rgba(0, 166, 192, 0.3);
  }

  .modern-card:hover::after {
    opacity: 1;
  }

  .glow-effect {
    position: relative;
    z-index: 1;
  }

  /* Dark mode glow effect */
  .dark .glow-effect {
    box-shadow: 0 0 8px rgba(0, 166, 192, 0.6), 0 0 16px rgba(0, 166, 192, 0.3);
    animation: pulse-glow-dark 3s ease-in-out infinite;
  }

  /* Light mode glow effect */
  .light .glow-effect {
    box-shadow: 0 0 6px rgba(0, 166, 192, 0.4), 0 0 12px rgba(0, 166, 192, 0.2);
    animation: pulse-glow-light 3s ease-in-out infinite;
  }

  .glow-effect::after {
    content: '';
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    z-index: -1;
    border-radius: inherit;
  }

  /* Dark mode glow effect after */
  .dark .glow-effect::after {
    background: radial-gradient(circle, rgba(0, 166, 192, 0.2), transparent 70%);
    opacity: 0.5;
  }

  /* Light mode glow effect after */
  .light .glow-effect::after {
    background: radial-gradient(circle, rgba(0, 166, 192, 0.15), transparent 70%);
    opacity: 0.4;
  }

  .glass-panel {
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    border-radius: 1rem;
    position: relative;
    overflow: hidden;
  }

  /* Dark mode glass panel */
  .dark .glass-panel {
    background: linear-gradient(135deg,
                rgba(0, 166, 192, 0.08),
                rgba(10, 20, 30, 0.9));
    border: 1px solid rgba(0, 166, 192, 0.2);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.4),
                inset 0 1px 0 0 rgba(255, 255, 255, 0.05),
                0 0 15px rgba(0, 166, 192, 0.1);
  }

  /* Light mode glass panel */
  .light .glass-panel {
    background: linear-gradient(135deg,
                rgba(255, 255, 255, 0.9),
                rgba(240, 250, 255, 0.85));
    border: 1px solid rgba(0, 166, 192, 0.15);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08),
                inset 0 1px 0 0 rgba(255, 255, 255, 0.9),
                0 0 15px rgba(0, 166, 192, 0.05);
  }

  .glass-panel::before {
    content: '';
    position: absolute;
    top: 0;
    left: -50%;
    width: 100%;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(0, 166, 192, 0.3), transparent);
    opacity: 0.5;
  }

  .section-heading {
    font-size: 1.875rem;
    line-height: 2.25rem;
    font-weight: 700;
    position: relative;
    display: inline-block;
    margin-bottom: 1.5rem;
  }

  @media (min-width: 768px) {
    .section-heading {
      font-size: 2.25rem;
      line-height: 2.5rem;
    }
  }

  .section-heading::after {
    content: '';
    position: absolute;
    bottom: -0.5rem;
    left: 0;
    width: 60%;
    height: 3px;
    background: linear-gradient(90deg, hsl(var(--primary)), transparent);
    border-radius: 9999px;
  }

  /* Enhanced text highlight styles */
  .highlight-text {
    position: relative;
    display: inline-block;
    z-index: 1;
    font-weight: 700;
    letter-spacing: -0.01em;
    color: hsl(var(--primary));
  }

  .highlight-text::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 30%;
    background: linear-gradient(90deg, hsl(var(--primary)/0.3), hsl(var(--accent)/0.2));
    z-index: -1;
    transform: skewX(-5deg) skewY(1deg);
    transition: all 0.3s ease;
    border-radius: 4px;
  }

  .highlight-text:hover::before {
    height: 55%;
    background: linear-gradient(90deg, hsl(var(--primary)/0.4), hsl(var(--accent)/0.3));
    transform: skewX(-8deg) skewY(1deg);
  }

  /* Gradient text effect */
  .gradient-text {
    background: linear-gradient(90deg, hsl(var(--primary)), hsl(var(--accent)));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    display: inline-block;
    font-weight: 700;
    letter-spacing: -0.02em;
    text-shadow: 0 2px 10px rgba(0, 166, 192, 0.2);
  }

  /* Grid pattern effect */
  .grid-pattern {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: 40px 40px;
    background-image:
      linear-gradient(to right, rgba(0, 166, 192, 0.1) 1.5px, transparent 1.5px),
      linear-gradient(to bottom, rgba(0, 166, 192, 0.1) 1.5px, transparent 1.5px);
    opacity: 0.5;
    pointer-events: none;
    z-index: 1;
    animation: grid-move 20s linear infinite, grid-fade 8s ease-in-out infinite;
    mask-image: radial-gradient(ellipse at center, rgba(0, 0, 0, 1) 50%, rgba(0, 0, 0, 0) 100%);
    -webkit-mask-image: radial-gradient(ellipse at center, rgba(0, 0, 0, 1) 50%, rgba(0, 0, 0, 0) 100%);
  }

  .dark .grid-pattern {
    background-image:
      linear-gradient(to right, rgba(0, 166, 192, 0.2) 1.5px, transparent 1.5px),
      linear-gradient(to bottom, rgba(0, 166, 192, 0.2) 1.5px, transparent 1.5px);
    box-shadow: 0 0 15px rgba(0, 166, 192, 0.1), 0 0 30px rgba(0, 166, 192, 0.05);
    filter: drop-shadow(0 0 8px rgba(0, 166, 192, 0.2));
  }

  .light .grid-pattern {
    background-image:
      linear-gradient(to right, rgba(0, 166, 192, 0.2) 1.5px, transparent 1.5px),
      linear-gradient(to bottom, rgba(0, 166, 192, 0.2) 1.5px, transparent 1.5px);
  }

  @keyframes grid-move {
    0% {
      background-position: 0 0;
    }
    100% {
      background-position: 40px 40px;
    }
  }

  @keyframes grid-fade {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 0.7; }
  }

  .grid-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: 80px 80px;
    background-image:
      linear-gradient(to right, rgba(0, 166, 192, 0.05) 1px, transparent 1px),
      linear-gradient(to bottom, rgba(0, 166, 192, 0.05) 1px, transparent 1px);
    opacity: 0.3;
    pointer-events: none;
    z-index: 1;
    animation: grid-overlay-move 30s linear infinite, grid-pulse 15s ease-in-out infinite;
    mask-image: radial-gradient(ellipse at center, rgba(0, 0, 0, 1) 60%, rgba(0, 0, 0, 0) 95%);
    -webkit-mask-image: radial-gradient(ellipse at center, rgba(0, 0, 0, 1) 60%, rgba(0, 0, 0, 0) 95%);
  }

  .dark .grid-overlay {
    background-image:
      linear-gradient(to right, rgba(0, 166, 192, 0.1) 1px, transparent 1px),
      linear-gradient(to bottom, rgba(0, 166, 192, 0.1) 1px, transparent 1px);
    filter: blur(0.5px) drop-shadow(0 0 10px rgba(0, 166, 192, 0.15));
    box-shadow: inset 0 0 20px rgba(0, 166, 192, 0.05);
  }

  .light .grid-overlay {
    background-image:
      linear-gradient(to right, rgba(0, 166, 192, 0.1) 1px, transparent 1px),
      linear-gradient(to bottom, rgba(0, 166, 192, 0.1) 1px, transparent 1px);
    filter: blur(0.5px);
  }

  @keyframes grid-overlay-move {
    0% {
      background-position: 0 0;
    }
    100% {
      background-position: 80px 80px;
    }
  }

  @keyframes grid-pulse {
    0%, 100% {
      opacity: 0.3;
      transform: scale(1);
    }
    50% {
      opacity: 0.4;
      transform: scale(1.05);
    }
  }

  .clean-input {
    background-color: hsl(var(--muted));
    border: 1px solid hsl(var(--border)/0.5);
    border-radius: 0.375rem;
    padding: 0.5rem 1rem;
    color: hsl(var(--foreground));
    transition: all 0.2s ease;
  }

  .clean-input:focus {
    outline: none;
    border-color: hsl(var(--primary)/0.7);
    box-shadow: 0 0 0 2px hsl(var(--primary)/0.2);
  }

  .modern-btn {
    padding: 0.625rem 1.25rem;
    border-radius: 0.375rem;
    transition: all 0.2s ease;
    font-weight: 500;
    position: relative;
    overflow: hidden;
  }

  .modern-btn::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to top, rgba(255,255,255,0.08), transparent);
    opacity: 0;
    transition: opacity 0.2s ease;
  }

  .modern-btn:hover::after {
    opacity: 1;
  }

  .modern-btn-primary {
    background-color: hsl(var(--primary));
    color: hsl(var(--primary-foreground));
  }

  .modern-btn-primary:hover {
    background-color: hsl(var(--primary)/0.9);
    box-shadow: 0 0 15px hsl(var(--primary)/0.4);
  }

  .modern-btn-secondary {
    background-color: hsl(var(--secondary));
    color: hsl(var(--secondary-foreground));
  }

  .modern-btn-secondary:hover {
    background-color: hsl(var(--secondary)/0.9);
    box-shadow: 0 0 15px hsl(var(--secondary)/0.4);
  }

  .modern-btn-outline {
    background-color: transparent;
    border: 1px solid hsl(var(--primary));
    color: hsl(var(--primary));
  }

  .modern-btn-outline:hover {
    background-color: hsl(var(--primary)/0.1);
    box-shadow: 0 0 15px hsl(var(--primary)/0.2);
  }
}

.noise-bg {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMDAiIGhlaWdodD0iMzAwIj48ZmlsdGVyIGlkPSJhIiB4PSIwIiB5PSIwIj48ZmVUdXJidWxlbmNlIGJhc2VGcmVxdWVuY3k9Ii43NSIgc3RpdGNoVGlsZXM9InN0aXRjaCIgdHlwZT0iZnJhY3RhbE5vaXNlIi8+PGZlQ29sb3JNYXRyaXggdHlwZT0ic2F0dXJhdGUiIHZhbHVlcz0iMCIvPjwvZmlsdGVyPjxwYXRoIGQ9Ik0wIDBoMzAwdjMwMEgweiIgZmlsdGVyPSJ1cmwoI2EpIiBvcGFjaXR5PSIuMDUiLz48L3N2Zz4=');
  background-repeat: repeat;
}

/* Card glow effect */
.card-glow {
  position: relative;
  z-index: 1;
  transition: all 0.3s ease;
}

.dark .card-glow {
  background: black;
  box-shadow: 0 0 15px rgba(0, 166, 192, 0.5),
              0 0 30px rgba(0, 166, 192, 0.3),
              0 0 45px rgba(0, 166, 192, 0.1);
  border: 1px solid rgba(0, 166, 192, 0.4);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

.light .card-glow {
  background: rgba(255, 255, 255, 0.8);
  box-shadow: 0 0 8px rgba(0, 166, 192, 0.3),
              0 0 16px rgba(0, 166, 192, 0.1);
  border: 1px solid rgba(0, 166, 192, 0.2);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

.card-glow::before {
  content: '';
  position: absolute;
  inset: 0;
  z-index: -2;
  background: radial-gradient(circle at center, rgba(0, 166, 192, 0.15), transparent 70%);
  opacity: 0;
  transition: all 0.5s ease;
}

.card-glow::after {
  content: '';
  position: absolute;
  inset: 0;
  z-index: -1;
  background: linear-gradient(120deg, rgba(255, 255, 255, 0) 30%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0) 70%);
  opacity: 0;
  transition: all 0.5s ease;
}

.dark .card-glow::before {
  opacity: 0.3;
}

.dark .card-glow::after {
  opacity: 0.1;
}

.dark .card-glow:hover {
  box-shadow: 0 0 20px rgba(0, 166, 192, 0.7),
              0 0 40px rgba(0, 166, 192, 0.4),
              0 0 60px rgba(0, 166, 192, 0.2);
  border-color: rgba(0, 166, 192, 0.6);
  transform: translateY(-3px) scale(1.01);
}

.light .card-glow:hover {
  box-shadow: 0 0 15px rgba(0, 166, 192, 0.4),
              0 0 30px rgba(0, 166, 192, 0.2);
  border-color: rgba(0, 166, 192, 0.4);
  transform: translateY(-3px) scale(1.01);
}

.dark .card-glow:hover::before {
  opacity: 0.5;
}

.dark .card-glow:hover::after {
  opacity: 0.2;
  background-position: 200% center;
}

.light .card-glow:hover::before {
  opacity: 0.3;
}

.light .card-glow:hover::after {
  opacity: 0.3;
  background-position: 200% center;
}

/* Neon effects - dark mode */
.dark .neon-border {
  box-shadow: 0 0 5px rgba(0, 166, 192, 0.7),
              0 0 10px rgba(0, 166, 192, 0.5),
              0 0 15px rgba(0, 166, 192, 0.3);
}

.dark .neon-border-secondary {
  box-shadow: 0 0 5px rgba(72, 215, 206, 0.7),
              0 0 10px rgba(72, 215, 206, 0.5),
              0 0 15px rgba(72, 215, 206, 0.3);
}

.dark .neon-text {
  text-shadow: 0 0 5px rgba(0, 166, 192, 0.7),
              0 0 10px rgba(0, 166, 192, 0.5);
}

.dark .neon-text-secondary {
  text-shadow: 0 0 5px rgba(72, 215, 206, 0.7),
              0 0 10px rgba(72, 215, 206, 0.5);
}

.dark .neon-text-accent {
  text-shadow: 0 0 5px rgba(72, 215, 206, 0.7),
              0 0 10px rgba(72, 215, 206, 0.5);
}

/* Neon effects - light mode (more subtle) */
.light .neon-border {
  box-shadow: 0 0 4px rgba(0, 166, 192, 0.4),
              0 0 8px rgba(0, 166, 192, 0.2);
}

.light .neon-border-secondary {
  box-shadow: 0 0 4px rgba(72, 215, 206, 0.4),
              0 0 8px rgba(72, 215, 206, 0.2);
}

.light .neon-text {
  text-shadow: 0 1px 3px rgba(0, 166, 192, 0.4);
  font-weight: 700;
}

.light .neon-text-secondary {
  text-shadow: 0 1px 3px rgba(72, 215, 206, 0.4);
  font-weight: 700;
}

.light .neon-text-accent {
  text-shadow: 0 1px 3px rgba(72, 215, 206, 0.4);
  font-weight: 700;
}

.scan-line {
  position: relative;
  overflow: hidden;
}

/* Dark mode scan line */
.dark .scan-line::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100%;
  background: linear-gradient(to bottom, transparent, rgba(0, 166, 192, 0.15), transparent);
  animation: scanning 3s ease-in-out infinite;
}

/* Light mode scan line */
.light .scan-line::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100%;
  background: linear-gradient(to bottom, transparent, rgba(0, 166, 192, 0.08), transparent);
  animation: scanning 3s ease-in-out infinite;
}

@keyframes scanning {
  0% { background-position: 0% 0%; }
  100% { background-position: 0% 100%; }
}

@keyframes glitch {
  0%, 100% { transform: translate(0); }
  20% { transform: translate(-2px, 2px); }
  40% { transform: translate(-2px, -2px); }
  60% { transform: translate(2px, 2px); }
  80% { transform: translate(2px, -2px); }
}

/* Dark mode pulse glow */
.dark .animate-pulse-glow {
  animation: pulse-glow-dark 2s infinite;
}

@keyframes pulse-glow-dark {
  0%, 100% {
    box-shadow: 0 0 5px rgba(0, 166, 192, 0.5), 0 0 10px rgba(0, 166, 192, 0.3);
    opacity: 0.8;
  }
  50% {
    box-shadow: 0 0 8px rgba(0, 166, 192, 0.7), 0 0 16px rgba(0, 166, 192, 0.5), 0 0 24px rgba(0, 166, 192, 0.3);
    opacity: 1;
  }
}

/* Light mode pulse glow */
.light .animate-pulse-glow {
  animation: pulse-glow-light 2s infinite;
}

@keyframes pulse-glow-light {
  0%, 100% {
    box-shadow: 0 0 3px rgba(0, 166, 192, 0.3), 0 0 6px rgba(0, 166, 192, 0.2);
    opacity: 0.8;
  }
  50% {
    box-shadow: 0 0 5px rgba(0, 166, 192, 0.5), 0 0 10px rgba(0, 166, 192, 0.3), 0 0 15px rgba(0, 166, 192, 0.2);
    opacity: 1;
  }
}

.glitch-hover:hover {
  animation: glitch 0.3s linear infinite;
}

/* Slow pulse animations for background glows */
@keyframes pulse-slow {
  0%, 100% {
    opacity: 0.5;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

@keyframes pulse-slow-reverse {
  0%, 100% {
    opacity: 0.8;
    transform: scale(1.05);
  }
  50% {
    opacity: 0.5;
    transform: scale(1);
  }
}

@keyframes browzy-pulse {
  0%, 100% {
    text-shadow: 0 0 10px rgba(0, 166, 192, 0.5),
                 0 0 20px rgba(0, 166, 192, 0.3);
    filter: brightness(1);
  }
  50% {
    text-shadow: 0 0 15px rgba(0, 166, 192, 0.7),
                 0 0 30px rgba(0, 166, 192, 0.5),
                 0 0 45px rgba(0, 166, 192, 0.3);
    filter: brightness(1.1);
  }
}

@keyframes browzy-glitch {
  0% {
    transform: translate(0);
    text-shadow: 0 0 10px rgba(0, 166, 192, 0.7);
  }
  25% {
    transform: translate(-1px, 1px);
    text-shadow: -1px 0 rgba(72, 215, 206, 0.7), 0 0 15px rgba(0, 166, 192, 0.8);
  }
  50% {
    transform: translate(1px, -1px);
    text-shadow: 1px 0 rgba(0, 166, 192, 0.7), 0 0 20px rgba(72, 215, 206, 0.8);
  }
  75% {
    transform: translate(-1px, -1px);
    text-shadow: -1px 0 rgba(72, 215, 206, 0.7), 0 0 15px rgba(0, 166, 192, 0.8);
  }
  100% {
    transform: translate(0);
    text-shadow: 0 0 10px rgba(0, 166, 192, 0.7);
  }
}

.animate-pulse-slow {
  animation: pulse-slow 8s ease-in-out infinite;
}

.animate-pulse-slow-reverse {
  animation: pulse-slow-reverse 10s ease-in-out infinite;
}

@keyframes chrome-logo-spin {
  0% {
    transform: rotate(0deg) scale(1);
    filter: brightness(1);
  }
  25% {
    transform: rotate(5deg) scale(1.05);
    filter: brightness(1.2);
  }
  50% {
    transform: rotate(0deg) scale(1);
    filter: brightness(1);
  }
  75% {
    transform: rotate(-5deg) scale(1.05);
    filter: brightness(1.2);
  }
  100% {
    transform: rotate(0deg) scale(1);
    filter: brightness(1);
  }
}

.animate-chrome-logo {
  animation: chrome-logo-spin 8s ease-in-out infinite;
}

@keyframes gradient-border-animation {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.animated-gradient-border {
  position: relative;
}

.animated-gradient-border::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg,
    hsl(var(--primary)),
    hsl(var(--accent)),
    hsl(var(--secondary)),
    hsl(var(--primary)));
  background-size: 400% 400%;
  z-index: -1;
  border-radius: inherit;
  animation: gradient-border-animation 15s ease infinite;
  opacity: 0.5;
  filter: blur(8px);
}

.font-heading {
  font-family: 'Styrene A', 'Styrene B', 'Helvetica Neue', Arial, sans-serif;
  font-weight: 600;
}

.font-body {
  font-family: 'Tiempos Text', Georgia, Cambria, 'Times New Roman', Times, serif;
}

.font-mono {
  font-family: 'Roboto Mono', monospace;
}

.font-inter {
  font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
}

/* Browzy brand text effect */
.browzy-text {
  position: relative;
  display: inline-block;
  background: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--accent)));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  font-weight: 800;
  letter-spacing: -0.02em;
  text-shadow: 0 2px 10px rgba(0, 166, 192, 0.3);
  padding: 0 0.1em;
  transform: translateY(0);
  transition: all 0.3s ease;
  animation: browzy-pulse 3s ease-in-out infinite;
}

.browzy-text::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(0, 166, 192, 0.2), rgba(72, 215, 206, 0.1));
  z-index: -1;
  border-radius: 4px;
  filter: blur(8px);
  opacity: 0.7;
  transform: translateY(0);
  transition: all 0.3s ease;
}

.browzy-text::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, transparent, hsl(var(--primary)), transparent);
  opacity: 0.8;
}

.browzy-text:hover {
  transform: translateY(-2px);
  text-shadow: 0 4px 12px rgba(0, 166, 192, 0.5);
  animation: browzy-glitch 0.3s ease-in-out;
}

.browzy-text:hover::before {
  opacity: 1;
  transform: translateY(-2px);
  filter: blur(12px);
}

.dark .browzy-text {
  text-shadow: 0 0 10px rgba(0, 166, 192, 0.7),
               0 0 20px rgba(0, 166, 192, 0.4);
}

.light .browzy-text {
  text-shadow: 0 2px 8px rgba(0, 166, 192, 0.4);
}
