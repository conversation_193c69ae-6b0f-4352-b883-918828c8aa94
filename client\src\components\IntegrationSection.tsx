import { motion } from 'framer-motion';
import { Button } from './ui/button';
import { Card, CardContent } from './ui/card';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from './ui/tabs';
import {
  Puzzle,
  Key,
  Chrome,
  ArrowRight,
  Copy,
  Check,
  Refresh<PERSON>w,
  <PERSON>rk<PERSON>
} from 'lucide-react';
import { useState } from 'react';
import { useToast } from '@/hooks/use-toast';

export default function IntegrationSection() {
  const [copied, setCopied] = useState(false);
  const { toast } = useToast();

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    setCopied(true);

    toast({
      title: "Copied to clipboard",
      description: "Text has been copied to your clipboard.",
    });

    setTimeout(() => setCopied(false), 2000);
  };

  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 }
  };

  return (
    <section id="integration" className="py-16 md:py-24 relative">
      {/* Background elements */}
      <div className="absolute inset-0 bg-gradient-to-b from-background/80 via-background to-background/90 z-0"></div>
      <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-primary/50 to-transparent"></div>
      <div className="absolute bottom-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-primary/50 to-transparent"></div>

      {/* Background glow */}
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[80%] h-[70%] bg-primary/5 rounded-full blur-3xl opacity-60 -z-10"></div>

      <div className="relative z-10 max-w-6xl mx-auto px-6 sm:px-8 lg:px-12">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
          className="text-center mb-16"
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.7, delay: 0.2 }}
            className="inline-block mb-3 px-5 py-2 bg-primary/10 rounded-full text-primary text-sm font-medium border border-primary/20"
          >
            <Puzzle className="w-4 h-4 inline-block mr-2" />
            Integration Guide
          </motion.div>

          <h2 className="font-inter text-5xl md:text-6xl font-bold tracking-tight">
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary">Connect</span> With The Extension
          </h2>
          <p className="font-inter text-lg md:text-xl text-muted-foreground mt-6 max-w-2xl mx-auto leading-relaxed">
            Follow these simple steps to set up and connect the browzy extension with your account and API keys.
          </p>
        </motion.div>

        <Tabs defaultValue="install" className="w-full">
          <TabsList className="grid w-full max-w-3xl mx-auto grid-cols-3 mb-12 p-1.5 bg-background/80 backdrop-blur-sm border-2 border-muted/50 shadow-lg rounded-xl">
            <TabsTrigger value="install" className="font-inter text-base py-3 rounded-lg data-[state=active]:bg-gradient-to-r data-[state=active]:from-primary/20 data-[state=active]:to-primary/10 data-[state=active]:text-primary data-[state=active]:border-b-2 data-[state=active]:border-primary transition-all duration-300">
              <Chrome className="w-5 h-5 mr-2" />
              <span className="hidden sm:inline font-medium">Install Extension</span>
              <span className="sm:hidden font-medium">Install</span>
            </TabsTrigger>
            <TabsTrigger value="connect" className="font-inter text-base py-3 rounded-lg data-[state=active]:bg-gradient-to-r data-[state=active]:from-primary/20 data-[state=active]:to-primary/10 data-[state=active]:text-primary data-[state=active]:border-b-2 data-[state=active]:border-primary transition-all duration-300">
              <Puzzle className="w-5 h-5 mr-2" />
              <span className="hidden sm:inline font-medium">Connect Account</span>
              <span className="sm:hidden font-medium">Connect</span>
            </TabsTrigger>
            <TabsTrigger value="apikey" className="font-inter text-base py-3 rounded-lg data-[state=active]:bg-gradient-to-r data-[state=active]:from-primary/20 data-[state=active]:to-primary/10 data-[state=active]:text-primary data-[state=active]:border-b-2 data-[state=active]:border-primary transition-all duration-300">
              <Key className="w-5 h-5 mr-2" />
              <span className="hidden sm:inline font-medium">Set API Key</span>
              <span className="sm:hidden font-medium">API Key</span>
            </TabsTrigger>
          </TabsList>

          <motion.div
            variants={container}
            initial="hidden"
            whileInView="show"
            viewport={{ once: true }}
          >
            {/* Install Extension Tab */}
            <TabsContent value="install">
              <Card className="border border-muted/50 shadow-lg bg-card/50 backdrop-blur-sm">
                <CardContent className="pt-6">
                  <div className="grid md:grid-cols-2 gap-8">
                    <motion.div variants={item} className="space-y-4">
                      <h3 className="text-2xl font-bold font-inter">Install the browzy Extension</h3>
                      <p className="text-muted-foreground font-inter">
                        Get started by installing the browzy extension from the Chrome Web Store. It only takes a few seconds to add to your browser.
                      </p>

                      <ol className="space-y-4 mt-6 font-inter">
                        <li className="flex items-start">
                          <div className="flex-shrink-0 w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center mr-3 mt-0.5">
                            <span className="text-primary font-semibold">1</span>
                          </div>
                          <div>
                            <p>Visit the Chrome Web Store and search for "browzy" or click the button below</p>
                          </div>
                        </li>
                        <li className="flex items-start">
                          <div className="flex-shrink-0 w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center mr-3 mt-0.5">
                            <span className="text-primary font-semibold">2</span>
                          </div>
                          <div>
                            <p>Click "Add to Chrome" and confirm the installation</p>
                          </div>
                        </li>
                        <li className="flex items-start">
                          <div className="flex-shrink-0 w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center mr-3 mt-0.5">
                            <span className="text-primary font-semibold">3</span>
                          </div>
                          <div>
                            <p>Once installed, you'll see the browzy icon in your browser toolbar</p>
                          </div>
                        </li>
                      </ol>

                      <div className="mt-8">
                        <Button className="bg-gradient-to-r from-primary to-secondary text-white shadow-md">
                          <Chrome className="mr-2 h-4 w-4" />
                          Get browzy Extension
                        </Button>
                      </div>
                    </motion.div>

                    <motion.div variants={item} className="relative">
                      <div className="absolute inset-0 bg-gradient-to-r from-primary/10 to-secondary/10 rounded-xl blur-md -z-10"></div>
                      <div className="bg-card border border-muted/50 rounded-xl p-6 h-full flex items-center justify-center">
                        <div className="text-center">
                          <div className="w-24 h-24 mx-auto bg-primary/10 rounded-xl flex items-center justify-center mb-4">
                            <Chrome className="w-12 h-12 text-primary" />
                          </div>
                          <h4 className="text-xl font-semibold font-inter mb-2">Chrome Extension</h4>
                          <p className="text-muted-foreground font-inter">
                            The browzy extension works with Chrome and other Chromium-based browsers like Edge and Brave.
                          </p>
                        </div>
                      </div>
                    </motion.div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Connect Account Tab */}
            <TabsContent value="connect">
              <Card className="border border-muted/50 shadow-lg bg-card/50 backdrop-blur-sm">
                <CardContent className="pt-6">
                  <div className="grid md:grid-cols-2 gap-8">
                    <motion.div variants={item} className="space-y-4">
                      <h3 className="text-2xl font-bold font-inter">Connect Your Account</h3>
                      <p className="text-muted-foreground font-inter">
                        Link your browzy account with the extension to sync your preferences, access your subscription features, and enable seamless browsing across devices.
                      </p>

                      <ol className="space-y-4 mt-6 font-inter">
                        <li className="flex items-start">
                          <div className="flex-shrink-0 w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center mr-3 mt-0.5">
                            <span className="text-primary font-semibold">1</span>
                          </div>
                          <div>
                            <p><strong>Sign in to your browzy account</strong> on the website</p>
                            <p className="text-sm text-muted-foreground mt-1">If you don't have an account yet, <a href="/signup" className="text-primary hover:underline">sign up here</a></p>
                          </div>
                        </li>
                        <li className="flex items-start">
                          <div className="flex-shrink-0 w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center mr-3 mt-0.5">
                            <span className="text-primary font-semibold">2</span>
                          </div>
                          <div>
                            <p><strong>Contact Support</strong> to get your <strong>Connection Token</strong></p>
                            <p className="text-sm text-muted-foreground mt-1">You'll find this in the sidebar menu under "Connection"</p>
                          </div>
                        </li>
                        <li className="flex items-start">
                          <div className="flex-shrink-0 w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center mr-3 mt-0.5">
                            <span className="text-primary font-semibold">3</span>
                          </div>
                          <div>
                            <p><strong>Generate a connection token</strong> by clicking the "Generate Token" button</p>
                            <p className="text-sm text-muted-foreground mt-1">This creates a unique token that links your extension to your account</p>
                          </div>
                        </li>
                        <li className="flex items-start">
                          <div className="flex-shrink-0 w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center mr-3 mt-0.5">
                            <span className="text-primary font-semibold">4</span>
                          </div>
                          <div>
                            <p><strong>Open the browzy extension</strong>, go to Settings, and paste your token in the "Connection Token" field</p>
                            <p className="text-sm text-muted-foreground mt-1">Click "Connect" to finalize the connection</p>
                          </div>
                        </li>
                        <li className="flex items-start">
                          <div className="flex-shrink-0 w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center mr-3 mt-0.5">
                            <span className="text-primary font-semibold">5</span>
                          </div>
                          <div>
                            <p><strong>Verify the connection</strong> is successful</p>
                            <p className="text-sm text-muted-foreground mt-1">You should see a confirmation message and your account details in the extension</p>
                          </div>
                        </li>
                      </ol>

                      <div className="mt-8 flex flex-wrap gap-3">
                        <Button className="bg-gradient-to-r from-primary to-secondary text-white shadow-md">
                          <ArrowRight className="mr-2 h-4 w-4" />
                          Go to Dashboard
                        </Button>
                        <Button variant="outline">
                          <RefreshCw className="mr-2 h-4 w-4" />
                          Manage Connections
                        </Button>
                      </div>
                    </motion.div>

                    <motion.div variants={item} className="relative">
                      <div className="absolute inset-0 bg-gradient-to-r from-primary/10 to-secondary/10 rounded-xl blur-md -z-10"></div>
                      <div className="bg-card border border-muted/50 rounded-xl p-6 h-full">
                        <h4 className="text-xl font-semibold font-inter mb-4">Connection Token</h4>
                        <div className="bg-background/50 rounded-lg p-4 border border-muted/50 relative mb-6">
                          <div className="font-mono text-sm break-all">brwz_conn_2f8a91c3e7d6b5a4908f7162d3c0e5b9</div>
                          <Button
                            size="icon"
                            variant="ghost"
                            className="absolute right-2 top-1/2 -translate-y-1/2 h-8 w-8"
                            onClick={() => copyToClipboard('brwz_conn_2f8a91c3e7d6b5a4908f7162d3c0e5b9')}
                          >
                            {copied ? <Check size={16} /> : <Copy size={16} />}
                          </Button>
                        </div>

                        <div className="bg-background/50 rounded-lg p-4 border border-muted/50 mb-6">
                          <h5 className="font-medium mb-2 font-inter">Token Information</h5>
                          <ul className="text-sm text-muted-foreground space-y-2 font-inter">
                            <li>• Tokens are valid for 30 days</li>
                            <li>• You can generate a new token at any time</li>
                            <li>• Generating a new token invalidates previous tokens</li>
                            <li>• Each account can have up to 3 active connections</li>
                          </ul>
                        </div>

                        <div className="bg-primary/10 rounded-lg p-4 border border-primary/20">
                          <h5 className="font-medium mb-2 font-inter flex items-center">
                            <Sparkles className="w-4 h-4 mr-2 text-primary" />
                            Benefits of Connecting
                          </h5>
                          <ul className="text-sm text-muted-foreground space-y-2 font-inter">
                            <li>• Sync your preferences across devices</li>
                            <li>• Access your subscription features</li>
                            <li>• Save and access your browsing history</li>
                            <li>• Receive updates and new features automatically</li>
                          </ul>
                        </div>
                      </div>
                    </motion.div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* API Key Tab */}
            <TabsContent value="apikey">
              <Card className="border border-muted/50 shadow-lg bg-card/50 backdrop-blur-sm">
                <CardContent className="pt-6">
                  <div className="grid md:grid-cols-2 gap-8">
                    <motion.div variants={item} className="space-y-4">
                      <h3 className="text-2xl font-bold font-inter">Set Up Your API Key</h3>
                      <p className="text-muted-foreground font-inter">
                        Browzy uses OpenRouter to connect to various AI models including OpenAI GPT-4, Anthropic Claude, and Google Gemini. You'll need to set up your API key to use the extension.
                      </p>

                      <ol className="space-y-4 mt-6 font-inter">
                        <li className="flex items-start">
                          <div className="flex-shrink-0 w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center mr-3 mt-0.5">
                            <span className="text-primary font-semibold">1</span>
                          </div>
                          <div>
                            <p><strong>Create an account at OpenRouter</strong></p>
                            <p className="text-sm text-muted-foreground mt-1">OpenRouter provides access to multiple AI models through a single API</p>
                            <div className="flex space-x-2 mt-2">
                              <Button variant="outline" size="sm" className="text-xs h-7" asChild>
                                <a href="https://openrouter.ai/signup" target="_blank" rel="noopener noreferrer">
                                  OpenRouter Signup
                                </a>
                              </Button>
                            </div>
                          </div>
                        </li>
                        <li className="flex items-start">
                          <div className="flex-shrink-0 w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center mr-3 mt-0.5">
                            <span className="text-primary font-semibold">2</span>
                          </div>
                          <div>
                            <p><strong>Generate an API key in OpenRouter</strong></p>
                            <p className="text-sm text-muted-foreground mt-1">Navigate to the API Keys section in your OpenRouter dashboard</p>
                            <div className="flex space-x-2 mt-2">
                              <Button variant="outline" size="sm" className="text-xs h-7" asChild>
                                <a href="https://openrouter.ai/keys" target="_blank" rel="noopener noreferrer">
                                  Get API Key
                                </a>
                              </Button>
                            </div>
                          </div>
                        </li>
                        <li className="flex items-start">
                          <div className="flex-shrink-0 w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center mr-3 mt-0.5">
                            <span className="text-primary font-semibold">3</span>
                          </div>
                          <div>
                            <p><strong>Open the browzy extension</strong> and go to Settings</p>
                            <p className="text-sm text-muted-foreground mt-1">Click on the browzy icon in your browser toolbar and select the settings icon</p>
                          </div>
                        </li>
                        <li className="flex items-start">
                          <div className="flex-shrink-0 w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center mr-3 mt-0.5">
                            <span className="text-primary font-semibold">4</span>
                          </div>
                          <div>
                            <p><strong>Navigate to the "API Keys" section</strong> in the settings menu</p>
                            <p className="text-sm text-muted-foreground mt-1">You'll see a field specifically for your OpenRouter API key</p>
                          </div>
                        </li>
                        <li className="flex items-start">
                          <div className="flex-shrink-0 w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center mr-3 mt-0.5">
                            <span className="text-primary font-semibold">5</span>
                          </div>
                          <div>
                            <p><strong>Paste your OpenRouter API key</strong> and click "Save"</p>
                            <p className="text-sm text-muted-foreground mt-1">Your key should start with "sk-or-..."</p>
                          </div>
                        </li>
                        <li className="flex items-start">
                          <div className="flex-shrink-0 w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center mr-3 mt-0.5">
                            <span className="text-primary font-semibold">6</span>
                          </div>
                          <div>
                            <p><strong>Select your preferred AI model</strong> in the Model Selection dropdown</p>
                            <p className="text-sm text-muted-foreground mt-1">You can choose from various models like GPT-4, Claude, or Gemini</p>
                          </div>
                        </li>
                      </ol>

                      <div className="mt-8 flex flex-wrap gap-3">
                        <Button className="bg-gradient-to-r from-primary to-secondary text-white shadow-md" asChild>
                          <a href="https://openrouter.ai/docs" target="_blank" rel="noopener noreferrer">
                            <Key className="mr-2 h-4 w-4" />
                            OpenRouter Documentation
                          </a>
                        </Button>
                      </div>
                    </motion.div>

                    <motion.div variants={item} className="relative">
                      <div className="absolute inset-0 bg-gradient-to-r from-primary/10 to-secondary/10 rounded-xl blur-md -z-10"></div>
                      <div className="bg-card border border-muted/50 rounded-xl p-6 h-full">
                        <h4 className="text-xl font-semibold font-inter mb-4">API Key Information</h4>

                        <div className="space-y-4">
                          <div className="bg-background/50 rounded-lg p-4 border border-muted/50 relative mb-6">
                            <h5 className="font-medium mb-2 font-inter">Example OpenRouter API Key</h5>
                            <div className="font-mono text-sm break-all">sk-or-v1-awds8f7g6h5j4k3l2...</div>
                            <p className="text-xs text-muted-foreground mt-2">Never share your API key with anyone</p>
                          </div>

                          <div className="bg-background/50 rounded-lg p-4 border border-muted/50">
                            <div className="flex items-center mb-2">
                              <Sparkles className="w-5 h-5 text-primary mr-2" />
                              <h5 className="font-medium font-inter">Available Models</h5>
                            </div>
                            <ul className="text-sm text-muted-foreground space-y-2 font-inter">
                              <li>• <strong>OpenAI:</strong> GPT-3.5-Turbo, GPT-4, GPT-4-Turbo</li>
                              <li>• <strong>Anthropic:</strong> Claude 3 Opus, Claude 3 Sonnet, Claude 3 Haiku</li>
                              <li>• <strong>Google:</strong> Gemini Pro, Gemini Ultra</li>
                              <li>• <strong>And more:</strong> Mistral, Llama, etc.</li>
                            </ul>
                          </div>

                          <div className="bg-background/50 rounded-lg p-4 border border-muted/50">
                            <div className="flex items-center mb-2">
                              <Key className="w-5 h-5 text-secondary mr-2" />
                              <h5 className="font-medium font-inter">OpenRouter Benefits</h5>
                            </div>
                            <ul className="text-sm text-muted-foreground space-y-2 font-inter">
                              <li>• Access to multiple AI models through a single API</li>
                              <li>• Free tier with generous usage limits</li>
                              <li>• Pay-as-you-go pricing for additional usage</li>
                              <li>• Fallback options if a model is unavailable</li>
                            </ul>
                          </div>

                          <div className="bg-primary/10 rounded-lg p-4 border border-primary/20">
                            <div className="flex items-center mb-2">
                              <Key className="w-5 h-5 text-primary mr-2" />
                              <h5 className="font-medium font-inter">Security Note</h5>
                            </div>
                            <p className="text-sm text-muted-foreground font-inter">
                              Your API keys are stored securely in your browser's local storage and are never sent to our servers. They are used only to communicate directly with OpenRouter's API.
                            </p>
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </motion.div>
        </Tabs>
      </div>
    </section>
  );
}
