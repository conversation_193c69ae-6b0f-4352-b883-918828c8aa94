import { motion } from 'framer-motion';
import { Button } from './ui/button';
import { ArrowRight, Activity } from 'lucide-react';
import ParticlesBackground from './ParticlesBackground';
import FloatingElements from './FloatingElements';

export default function HeroSection() {
  return (
    <section className="min-h-screen pt-16 md:pt-20 pb-16 md:pb-20 relative overflow-hidden full-bleed-grid px-4 sm:px-6 flex items-center">
      {/* Modern animated background effects */}
      <div className="absolute inset-0 bg-gradient-to-b from-background/80 via-background to-background/90 z-0"></div>

      {/* Particles background */}
      <ParticlesBackground />

      {/* Floating elements */}
      <FloatingElements />

      {/* Grid pattern background effect */}
      <div className="grid-pattern absolute inset-0 opacity-30"></div>
      <div className="grid-overlay absolute inset-0 opacity-20"></div>

      {/* Background glow effects */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* Primary glow - centered top */}
        <div className="absolute top-[5%] left-1/2 transform -translate-x-1/2 w-[70%] h-[60%] bg-primary/20 rounded-full blur-[180px] animate-pulse-slow"></div>

        {/* Secondary glow - bottom */}
        <div className="absolute bottom-[10%] left-1/2 transform -translate-x-1/2 w-[65%] h-[55%] bg-accent/20 rounded-full blur-[150px] animate-pulse-slow-reverse"></div>

        {/* Additional subtle glows */}
        <div className="absolute top-[25%] left-[10%] w-[40%] h-[40%] bg-secondary/15 rounded-full blur-[120px] animate-pulse-slow"></div>
        <div className="absolute top-[20%] right-[10%] w-[40%] h-[40%] bg-primary/15 rounded-full blur-[130px] animate-pulse-slow-reverse"></div>

        {/* Extra accent glows */}
        <div className="absolute bottom-[30%] left-[20%] w-[25%] h-[25%] bg-accent/10 rounded-full blur-[80px] animate-float"></div>
        <div className="absolute top-[40%] right-[20%] w-[20%] h-[20%] bg-primary/10 rounded-full blur-[70px] animate-float"></div>
      </div>

      {/* Noise texture overlay */}
      <div className="absolute inset-0 noise-bg opacity-[0.04] mix-blend-overlay pointer-events-none"></div>

      <div className="relative z-10 max-w-[94%] lg:max-w-[92%] xl:max-w-[88%] mx-auto">
        <div className="flex flex-col items-center text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="max-w-3xl"
          >
            <motion.div
              className="relative inline-flex items-center px-4 sm:px-6 py-2 sm:py-3 rounded-full bg-secondary/25 text-secondary mb-6 sm:mb-8 md:mb-10 text-xs sm:text-sm md:text-base lg:text-lg font-medium border-2 border-secondary/30 shadow-xl font-inter"
              initial={{ opacity: 0, y: -20 }}
              animate={{
                opacity: 1,
                y: 0,
                boxShadow: [
                  "0 10px 25px rgba(var(--secondary), 0.2)",
                  "0 10px 35px rgba(var(--secondary), 0.4)",
                  "0 10px 25px rgba(var(--secondary), 0.2)"
                ],
                transition: {
                  boxShadow: {
                    repeat: Infinity,
                    duration: 2
                  }
                }
              }}
              transition={{ duration: 0.5 }}
              whileHover={{ scale: 1.05, backgroundColor: "rgba(var(--secondary), 0.3)" }}
            >
              <div className="absolute -inset-1 bg-secondary/20 rounded-full blur-md -z-10 animate-pulse-slow"></div>
              <Activity className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 mr-2 sm:mr-3" />
              <span className="font-semibold tracking-wide text-xs sm:text-sm md:text-base" style={{ textShadow: '0 1px 2px rgba(0,0,0,0.15)' }}>POWERED BY TRUEAILABS</span>
              <div className="ml-2 sm:ml-3 w-2 h-2 sm:w-3 sm:h-3 rounded-full bg-secondary animate-pulse"></div>
            </motion.div>

            <h1 className="font-inter text-4xl sm:text-5xl md:text-6xl lg:text-7xl xl:text-8xl font-bold leading-tight tracking-tight">
              <motion.span
                className="block"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                    <span className="font-inter relative flex flex-wrap justify-center">
                      <span className="bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary inline-block transform hover:scale-105 transition-transform duration-300 relative">
                        Browzy
                      </span>
                      <span className="mx-1 sm:mx-2 relative inline-block">
                        -
                        <span className="absolute top-1/2 left-0 w-full h-0.5 bg-gradient-to-r from-transparent via-primary/30 to-transparent transform -translate-y-1/2"></span>
                      </span>
                      <span className="text-foreground relative">
                        Supercharge
                        <span className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-primary/30 to-transparent rounded-full"></span>
                      </span>
                    </span>
                    <motion.span
                      className="font-bold block mt-2 sm:mt-3 md:mt-4 text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl"
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: 0.3 }}
                    >
                      <div className="flex flex-wrap items-center justify-center gap-2 md:gap-3 lg:gap-4 relative">
                        {/* Background glow for the entire line */}
                        <div className="absolute inset-0 bg-gradient-to-r from-primary/5 via-accent/10 to-primary/5 rounded-full blur-md -z-10"></div>

                        <span className="text-foreground font-inter relative mr-0" style={{ textShadow: '0 2px 4px rgba(0,0,0,0.2)' }}>
                          Your
                          <span className="absolute -bottom-1 sm:-bottom-2 left-0 w-full h-0.5 bg-gradient-to-r from-transparent via-primary/40 to-transparent"></span>
                        </span>

                        <div className="relative group mx-1">
                          {/* Outer glow */}
                          <div className="absolute -inset-2 sm:-inset-3 bg-gradient-to-r from-primary/30 to-accent/30 rounded-full blur-xl opacity-75 group-hover:opacity-100 transition-opacity duration-500 animate-pulse-slow"></div>

                          {/* Inner glow */}
                          <div className="absolute -inset-1 sm:-inset-2 bg-gradient-to-r from-primary/40 to-accent/40 rounded-full blur-md opacity-75 group-hover:opacity-100 transition-opacity duration-500"></div>

                          {/* Chrome logo container */}
                          <div className="w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 lg:w-14 lg:h-14 inline-block relative z-10 flex items-center justify-center">
                            {/* Shine effect */}
                            <div className="absolute inset-0 bg-gradient-to-tr from-transparent via-primary/20 to-transparent rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                            {/* Chrome logo */}
                            <img
                              src="/images/chrome-logo.png"
                              alt="Chrome Logo"
                              className="w-6 h-6 sm:w-8 sm:h-8 md:w-10 md:h-10 lg:w-12 lg:h-12 object-contain animate-chrome-logo drop-shadow-xl"
                              style={{
                                filter: 'drop-shadow(0 0 12px rgba(var(--primary), 0.7))',
                                objectFit: 'contain',
                                aspectRatio: '1/1'
                              }}
                            />
                          </div>
                        </div>

                        <span className="text-foreground font-inter relative ml-0" style={{ textShadow: '0 2px 4px rgba(0,0,0,0.2)' }}>
                          Browser
                          <span className="absolute -bottom-1 sm:-bottom-2 left-0 w-full h-0.5 bg-gradient-to-r from-transparent via-primary/40 to-transparent"></span>
                        </span>

                        <div className="relative group mx-1">
                          {/* Outer glow */}
                          <div className="absolute -inset-2 sm:-inset-3 bg-gradient-to-r from-secondary/30 to-primary/30 rounded-full blur-xl opacity-75 group-hover:opacity-100 transition-opacity duration-500 animate-pulse-slow"></div>

                          {/* Inner glow */}
                          <div className="absolute -inset-1 sm:-inset-2 bg-gradient-to-r from-secondary/40 to-primary/40 rounded-full blur-md opacity-75 group-hover:opacity-100 transition-opacity duration-500"></div>

                          {/* Brave logo container */}
                          <div className="w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 lg:w-14 lg:h-14 inline-block relative z-10 flex items-center justify-center">
                            {/* Shine effect */}
                            <div className="absolute inset-0 bg-gradient-to-tr from-transparent via-secondary/20 to-transparent rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                            {/* Brave logo */}
                            <img
                              src="/images/brave-logo.png"
                              alt="Brave Logo"
                              className="w-6 h-6 sm:w-8 sm:h-8 md:w-10 md:h-10 lg:w-24 lg:h-24 object-contain drop-shadow-xl animate-float"
                              style={{
                                filter: 'drop-shadow(0 0 12px rgba(var(--secondary), 0.7))',
                                objectFit: 'contain',
                                aspectRatio: '1/1'
                              }}
                            />
                          </div>
                        </div>

                        <span className="bg-clip-text text-transparent bg-gradient-to-r from-accent via-primary to-accent bg-[length:200%_100%] animate-shimmer ml-1 sm:ml-2 md:ml-3 relative">
                          With AI
                          <span className="absolute -bottom-1 sm:-bottom-2 left-0 w-full h-0.5 sm:h-1 bg-gradient-to-r from-primary/50 to-accent/50 rounded-full"></span>
                        </span>
                      </div>
                    </motion.span>
              </motion.span>

            </h1>

            <motion.p
              className="mt-4 sm:mt-6 md:mt-8 text-sm sm:text-base md:text-lg lg:text-xl text-muted-foreground mx-auto font-inter max-w-2xl leading-relaxed bg-card/30 backdrop-blur-sm p-3 sm:p-4 md:p-5 rounded-lg sm:rounded-xl border border-muted/30 shadow-sm"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
            >
              Meet <span className="bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary font-semibold">browzy</span>, your new <span className="text-foreground font-semibold">happy digital companion</span> with questionable morals and unquestionable intelligence. Analyzes websites, delivers insights, and <span className="italic text-secondary">cheerfully judges your browsing habits</span>.
            </motion.p>

            <motion.div
              className="mt-6 sm:mt-8 md:mt-10 flex flex-col sm:flex-row gap-4 sm:gap-5 md:gap-7 justify-center"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.5 }}
            >
              <div className="relative group">
                <div className="absolute -inset-0.5 bg-gradient-to-r from-primary to-secondary rounded-xl blur opacity-70 group-hover:opacity-100 transition duration-500 group-hover:duration-200 animate-pulse-slow"></div>
                <Button
                  className="w-full sm:w-auto bg-gradient-to-r from-primary to-secondary text-white shadow-xl text-sm sm:text-base md:text-lg py-4 sm:py-5 md:py-6 lg:py-7 px-6 sm:px-8 md:px-10 rounded-lg sm:rounded-xl transition-all duration-300 transform hover:scale-105 relative"
                  size="lg"
                >
                  <a href="#download" className="font-medium flex items-center font-inter">
                    Install Now
                    <ArrowRight className="ml-3 h-5 w-5 group-hover:translate-x-1 transition-transform duration-300" />
                  </a>
                </Button>
              </div>

              <div className="relative group">
                <div className="absolute -inset-0.5 bg-gradient-to-r from-secondary/50 to-secondary/30 rounded-xl blur opacity-30 group-hover:opacity-70 transition duration-500 group-hover:duration-200"></div>
                <Button
                  variant="outline"
                  className="w-full sm:w-auto border-2 border-secondary text-secondary hover:bg-secondary/10 text-sm sm:text-base md:text-lg py-4 sm:py-5 md:py-6 lg:py-7 px-6 sm:px-8 md:px-10 rounded-lg sm:rounded-xl transition-all duration-300 transform hover:scale-105 relative"
                  size="lg"
                >
                  <a href="#how-it-works" className="font-medium font-inter flex items-center">
                    See How It Works
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="ml-3 opacity-70 group-hover:opacity-100 transition-opacity duration-300">
                      <circle cx="12" cy="12" r="10"></circle>
                      <polygon points="10 8 16 12 10 16 10 8"></polygon>
                    </svg>
                  </a>
                </Button>
              </div>
            </motion.div>


          </motion.div>
        </div>

        <div className="mt-16 sm:mt-20 md:mt-24 lg:mt-28 mb-12 sm:mb-16 md:mb-20 relative">
          <div className="h-px w-full bg-gradient-to-r from-transparent via-primary/50 to-transparent"></div>

          <div className="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 w-16 sm:w-20 md:w-24 h-16 sm:h-20 md:h-24">
            <div className="absolute inset-0 bg-gradient-to-r from-primary/40 to-accent/40 rounded-full blur-xl animate-pulse-slow"></div>
            <div className="absolute inset-3 bg-gradient-to-r from-primary/30 to-accent/30 rounded-full blur-md animate-pulse-slow-reverse"></div>

            <motion.div
              className="absolute inset-4 sm:inset-5 md:inset-6 flex items-center justify-center bg-background/80 backdrop-blur-sm rounded-full border border-primary/30"
              whileHover={{ scale: 1.1 }}
              transition={{ type: "spring", stiffness: 400, damping: 10 }}
            >
              <ArrowRight className="w-5 h-5 sm:w-6 sm:h-6 md:w-7 md:h-7 text-primary" />
            </motion.div>
          </div>

          <div className="absolute left-1/4 top-1/2 transform -translate-x-1/2 -translate-y-1/2 w-8 sm:w-10 md:w-12 h-8 sm:h-10 md:h-12">
            <div className="absolute inset-0 bg-secondary/20 rounded-full blur-lg animate-pulse-slow-reverse"></div>
          </div>

          <div className="absolute right-1/4 top-1/2 transform translate-x-1/2 -translate-y-1/2 w-8 sm:w-10 md:w-12 h-8 sm:h-10 md:h-12">
            <div className="absolute inset-0 bg-accent/20 rounded-full blur-lg animate-pulse-slow"></div>
          </div>
        </div>
      </div>
    </section>
  );
}
