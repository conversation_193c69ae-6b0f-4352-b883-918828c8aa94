import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RefreshCw, Copy, Check, Key } from 'lucide-react';
import { extensionConnector } from '@/lib/extension-connector';
import { useToast } from '@/hooks/use-toast';

interface TokenGeneratorProps {
  isConnected: boolean;
}

export default function TokenGenerator({ isConnected }: TokenGeneratorProps) {
  const { toast } = useToast();
  const [token, setToken] = useState<string>('');
  const [expiresAt, setExpiresAt] = useState<Date | null>(null);
  const [copied, setCopied] = useState(false);
  const [timeLeft, setTimeLeft] = useState<string>('');

  // Load token on mount
  useEffect(() => {
    loadToken();

    // Set up timer to update time left
    const interval = setInterval(() => {
      updateTimeLeft();
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  // Load the current token
  const loadToken = () => {
    const tokenData = extensionConnector.getCurrentToken();
    if (tokenData) {
      setToken(tokenData.token);
      setExpiresAt(new Date(tokenData.expiresAt));
    }
  };

  // Update the time left display
  const updateTimeLeft = () => {
    if (expiresAt) {
      const now = new Date();
      const diff = expiresAt.getTime() - now.getTime();

      if (diff <= 0) {
        setTimeLeft('Expired');
        // Generate a new token
        refreshToken();
        return;
      }

      // Calculate hours, minutes, seconds
      const hours = Math.floor(diff / (1000 * 60 * 60));
      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((diff % (1000 * 60)) / 1000);

      setTimeLeft(`${hours}h ${minutes}m ${seconds}s`);
    }
  };

  // Generate a new token
  const refreshToken = () => {
    const tokenData = extensionConnector.generateToken();
    setToken(tokenData.token);
    setExpiresAt(new Date(tokenData.expiresAt));

    toast({
      title: "Token Refreshed",
      description: "A new connection token has been generated.",
    });
  };

  // Copy token to clipboard
  const copyToken = () => {
    navigator.clipboard.writeText(token);
    setCopied(true);

    toast({
      title: "Token Copied",
      description: "Connection token copied to clipboard.",
    });

    setTimeout(() => setCopied(false), 2000);
  };

  return (
    <Card className="w-full bg-card/30 backdrop-blur-sm border border-border/20 overflow-hidden">
      <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-transparent"></div>
      <div className="relative">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                <Key className="h-4 w-4 text-primary" />
              </div>
              <div>
                <CardTitle>Connection Token</CardTitle>
                <CardDescription>
                  {isConnected
                    ? "Your extension is connected"
                    : "Enter this token in your extension to connect"}
                </CardDescription>
              </div>
            </div>
            {expiresAt && (
              <div className="text-xs text-muted-foreground">
                Expires in: <span className="font-mono">{timeLeft}</span>
              </div>
            )}
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="token">Your unique token</Label>
              <div className="flex gap-2">
                <Input
                  id="token"
                  value={token}
                  readOnly
                  className="font-mono text-center text-lg tracking-wider"
                />
                <Button
                  variant="outline"
                  size="icon"
                  onClick={copyToken}
                  className="flex-shrink-0"
                >
                  {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                </Button>
              </div>
            </div>

            {!isConnected && (
              <div className="space-y-2 text-sm text-muted-foreground">
                <p>1. Copy this token</p>
                <p>2. Open the browzy extension</p>
                <p>3. Go to Settings and paste the token</p>
                <p>4. Click "Connect" in the extension</p>
              </div>
            )}
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button
            variant="outline"
            size="sm"
            onClick={refreshToken}
            className="gap-1"
          >
            <RefreshCw className="h-3 w-3 mr-1" />
            Refresh Token
          </Button>
        </CardFooter>
      </div>
    </Card>
  );
}
