

export default function Footer() {
  return (
    <footer className="dark:bg-black light:bg-white py-20 md:py-24 border-t border-muted relative">
        {/* Background glow effects */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none opacity-30">
          <div className="absolute bottom-0 left-1/4 w-[40%] h-[40%] bg-primary/10 rounded-full blur-[100px] animate-pulse-slow"></div>
          <div className="absolute top-0 right-1/4 w-[30%] h-[30%] bg-secondary/10 rounded-full blur-[80px] animate-pulse-slow-reverse"></div>
        </div>

        <div className="grid md:grid-cols-4 gap-12 md:gap-16 relative z-10">
          <div>
            <div className="flex items-center space-x-3 mb-8">
              <div className="w-10 h-10 rounded-full flex items-center justify-center shadow-lg transform transition-transform duration-300 hover:scale-110 overflow-hidden">
                <img src="/images/browzy-logo.png" alt="Browzy Logo" className="h-full w-full object-cover" />
              </div>
              <span className="font-inter font-bold text-2xl bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary">
                browzy
              </span>
            </div>

            <p className="text-muted-foreground text-base leading-relaxed font-inter">
              Bringing sarcasm and questionable judgment to your browsing experience since 2023.
            </p>

            <div className="flex space-x-5 mt-6">
              {[
                { icon: "twitter", href: "https://twitter.com/browzyai" },
                { icon: "github", href: "https://github.com/browzyai" },
                { icon: "discord", href: "https://discord.gg/browzyai" },
                { icon: "product-hunt", href: "https://www.producthunt.com/products/browzy" },
              ].map((social) => (
                <a
                  key={social.icon}
                  href={social.href}
                  className="text-muted-foreground hover:text-primary transition-all duration-300 transform hover:scale-110 w-10 h-10 bg-muted/50 rounded-lg flex items-center justify-center shadow-sm"
                >
                  <i className={`fab fa-${social.icon} text-lg`}></i>
                </a>
              ))}
            </div>
          </div>

          <div>
            <h3 className="font-inter text-xl mb-6 text-foreground font-semibold bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary">Resources</h3>
            <ul className="space-y-4 text-muted-foreground font-inter">
              {[
                { label: "Documentation", href: "/documentation" },
                { label: "API", href: "/documentation?tab=api" },
                { label: "Privacy Policy", href: "/privacy-policy" },
                { label: "Terms of Service", href: "#" },
              ].map((link) => (
                <li key={link.label}>
                  <a href={link.href} className="hover:text-primary transition-all duration-300 transform hover:translate-x-1 inline-block text-base">
                    {link.label}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          <div>
            <h3 className="font-inter text-xl mb-6 text-foreground font-semibold bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary">About</h3>
            <ul className="space-y-4 text-muted-foreground font-inter">
              {[
                { label: "Our Story", href: "#" },
                { label: "Team", href: "#" },
                { label: "Blog", href: "#" },
                { label: "Careers (We're Not Hiring)", href: "#" },
              ].map((link) => (
                <li key={link.label}>
                  <a href={link.href} className="hover:text-primary transition-all duration-300 transform hover:translate-x-1 inline-block text-base">
                    {link.label}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          <div>
            <h3 className="font-inter text-xl mb-6 text-foreground font-semibold bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary">Newsletter</h3>
            <p className="text-muted-foreground text-base mb-5 leading-relaxed font-inter">
              Subscribe for updates and sarcastic observations. No spam, just judgment.
            </p>

            <form className="flex">
              <input
                type="email"
                placeholder="<EMAIL>"
                className="flex-grow bg-card border-2 border-muted rounded-l-lg p-3 text-foreground focus:border-primary focus:outline-none transition-all duration-300 text-base font-inter"
              />
              <button
                type="submit"
                className="bg-gradient-to-r from-primary to-secondary text-background px-5 rounded-r-lg hover:opacity-90 transition-all duration-300 shadow-md"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="w-5 h-5"
                >
                  <path d="m22 2-7 20-4-9-9-4Z" />
                  <path d="M22 2 11 13" />
                </svg>
              </button>
            </form>

            <p className="text-xs text-muted-foreground mt-3 italic font-inter">
              By subscribing, you agree to receive our newsletter and accept our <a href="/privacy-policy" className="text-primary hover:underline">privacy policy</a>.
            </p>
          </div>
        </div>

        <div className="border-t border-muted mt-20 pt-10 flex flex-col md:flex-row justify-between items-center">
          <div className="text-muted-foreground text-base font-inter">
            © 2025 <span className="text-primary font-medium">browzy</span>. All rights reserved. Especially the right to judge you.
          </div>

          <div className="text-muted-foreground text-base mt-6 md:mt-0 flex items-center font-inter">
            Made by <span className="text-red-500 mx-2 animate-pulse">trueailabs</span> and questionable ethics
          </div>
        </div>
    </footer>
  );
}
