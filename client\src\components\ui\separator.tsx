"use client"

import * as React from "react"
import * as SeparatorPrimitive from "@radix-ui/react-separator"
import { cn } from "@/lib/utils"
import { useEffect, useRef, useState } from "react"

const Separator = React.forwardRef<
  React.ElementRef<typeof SeparatorPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof SeparatorPrimitive.Root>
>(
  (
    { className, orientation = "horizontal", decorative = true, ...props },
    ref
  ) => (
    <SeparatorPrimitive.Root
      ref={ref}
      decorative={decorative}
      orientation={orientation}
      className={cn(
        "shrink-0 bg-border",
        orientation === "horizontal" ? "h-[1px] w-full" : "h-full w-[1px]",
        className
      )}
      {...props}
    />
  )
)
Separator.displayName = SeparatorPrimitive.Root.displayName

export interface CyberpunkSeparatorProps extends React.HTMLAttributes<HTMLDivElement> {
  animate?: boolean;
}

const CyberpunkSeparator = ({ 
  className, 
  animate = true,
  ...props 
}: CyberpunkSeparatorProps) => {
  const [isVisible, setIsVisible] = useState(false);
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!animate) {
      setIsVisible(true);
      return;
    }

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.disconnect();
        }
      },
      { threshold: 0.1 }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => {
      observer.disconnect();
    };
  }, [animate]);

  return (
    <div 
      ref={ref}
      className={cn(
        "h-4 bg-transparent relative my-20",
        isVisible && animate ? "animate-pulse-glow" : "",
        className
      )}
      {...props}
    >
      <div className="h-1 bg-gradient-to-r from-transparent via-primary to-transparent absolute inset-0 top-1/2 transform -translate-y-1/2" />
      <div className="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 h-3 w-3 bg-primary rounded-full shadow-glow" />
    </div>
  );
};

export { Separator, CyberpunkSeparator }
