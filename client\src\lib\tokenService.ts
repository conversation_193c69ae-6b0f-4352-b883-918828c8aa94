import { supabase } from './supabase';

/**
 * Saves a connection token to the user's account
 * @param userId The user's ID
 * @param token The connection token to save
 * @returns A promise that resolves when the token is saved
 */
export async function saveUserToken(userId: string, token: string): Promise<{ success: boolean; error?: any }> {
  try {
    // Call the API to save the token
    const response = await fetch('/api/extension/token/generate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include', // Include cookies in the request
      body: JSON.stringify({ token }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to save token');
    }

    const data = await response.json();

    // Also store in localStorage for backup
    localStorage.setItem(`user_token_${userId}`, token);

    return { success: data.success };
  } catch (error) {
    console.error('Error saving user token:', error);
    return { success: false, error: error instanceof Error ? error.message : String(error) };
  }
}

/**
 * Gets the current connection token for a user
 * @returns The user's current token information, or null if none exists
 */
export async function getUserToken(): Promise<{
  success: boolean;
  token?: string | null;
  tokenId?: string | null;
  createdAt?: string | null;
  lastUsed?: string | null;
  error?: any;
}> {
  try {
    // Call the API to get the token
    const response = await fetch('/api/extension/token', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include', // Include cookies in the request
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to get token');
    }

    const data = await response.json();

    return {
      success: data.success,
      token: data.token,
      tokenId: data.tokenId,
      createdAt: data.createdAt,
      lastUsed: data.lastUsed
    };
  } catch (error) {
    console.error('Error getting user token:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Revokes the current connection token for a user
 * @returns Success status
 */
export async function revokeUserToken(): Promise<{ success: boolean; error?: any }> {
  try {
    // Call the API to revoke the token
    const response = await fetch('/api/extension/token', {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include', // Include cookies in the request
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to revoke token');
    }

    const data = await response.json();

    return { success: data.success };
  } catch (error) {
    console.error('Error revoking user token:', error);
    return { success: false, error: error instanceof Error ? error.message : String(error) };
  }
}

/**
 * Validates a connection token against a user
 * @param token The token to validate
 * @returns Information about the token's validity and associated user
 */
export async function validateConnectionToken(token: string): Promise<{
  valid: boolean;
  userId?: string;
  username?: string;
  error?: any
}> {
  try {
    // Call the API to validate the token
    const response = await fetch('/api/extension/token/validate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include', // Include cookies in the request
      body: JSON.stringify({ token }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to validate token');
    }

    const data = await response.json();

    return {
      valid: data.success,
      userId: data.userId,
      username: data.username
    };
  } catch (error) {
    console.error('Error validating connection token:', error);
    return { valid: false, error: error instanceof Error ? error.message : String(error) };
  }
}
