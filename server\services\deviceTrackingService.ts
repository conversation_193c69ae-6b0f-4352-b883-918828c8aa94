import { supabaseAdmin } from "../supabase";

/**
 * Device information interface
 */
interface DeviceInfo {
  fingerprint: string;
  ipAddress: string;
  userAgent?: string;
  browserName?: string;
  browserVersion?: string;
  osName?: string;
  osVersion?: string;
  deviceType?: string;
}

/**
 * Track a device fingerprint for a user
 * @param userId User ID
 * @param deviceInfo Device information
 * @returns Success status
 */
export async function trackDeviceFingerprint(
  userId: string,
  deviceInfo: DeviceInfo
): Promise<{ success: boolean; error?: any }> {
  try {
    console.log(`Tracking device fingerprint for user ${userId}:`, deviceInfo.fingerprint);

    // Update or insert device fingerprint record
    const { error } = await supabaseAdmin
      .from('device_fingerprints')
      .upsert({
        user_id: userId,
        fingerprint: deviceInfo.fingerprint,
        ip_address: deviceInfo.ipAddress,
        last_seen: new Date().toISOString(),
        request_count: supabaseAdmin.sql`request_count + 1`
      }, { 
        onConflict: 'user_id, fingerprint',
        ignoreDuplicates: false
      });

    if (error) {
      console.error('Error tracking device fingerprint:', error);
      throw error;
    }

    // Track IP address usage
    await trackIpAddress(deviceInfo.ipAddress);

    console.log(`Device fingerprint tracked successfully for user: ${userId}`);
    return { success: true };
  } catch (error) {
    console.error('Error tracking device fingerprint:', error);
    return { success: false, error };
  }
}

/**
 * Track IP address usage
 * @param ipAddress IP address
 * @returns Success status
 */
export async function trackIpAddress(
  ipAddress: string
): Promise<{ success: boolean; error?: any }> {
  try {
    console.log(`Tracking IP address: ${ipAddress}`);

    // Update or insert IP address record
    const { error } = await supabaseAdmin
      .from('ip_usage_tracking')
      .upsert({
        ip_address: ipAddress,
        last_seen: new Date().toISOString(),
        request_count: supabaseAdmin.sql`request_count + 1`
      }, { 
        onConflict: 'ip_address',
        ignoreDuplicates: false
      });

    if (error) {
      console.error('Error tracking IP address:', error);
      throw error;
    }

    console.log(`IP address tracked successfully: ${ipAddress}`);
    return { success: true };
  } catch (error) {
    console.error('Error tracking IP address:', error);
    return { success: false, error };
  }
}

/**
 * Check if a device has been used with multiple accounts
 * @param deviceFingerprint Device fingerprint
 * @param currentUserId Current user ID
 * @returns Information about multiple account usage
 */
export async function checkMultipleAccountsOnDevice(
  deviceFingerprint: string,
  currentUserId: string
): Promise<{
  success: boolean;
  isMultipleAccounts: boolean;
  accountCount: number;
  error?: any;
}> {
  try {
    console.log(`Checking multiple accounts for device: ${deviceFingerprint}`);

    // Get all users who have used this device
    const { data, error } = await supabaseAdmin
      .from('device_fingerprints')
      .select('user_id')
      .eq('fingerprint', deviceFingerprint);

    if (error) {
      console.error('Error checking multiple accounts on device:', error);
      throw error;
    }

    // Count unique user IDs
    const uniqueUserIds = new Set(data?.map(item => item.user_id) || []);
    const accountCount = uniqueUserIds.size;

    // Check if this device has been used with multiple accounts
    const isMultipleAccounts = accountCount > 1;

    if (isMultipleAccounts) {
      console.log(`Device ${deviceFingerprint} has been used with ${accountCount} accounts`);

      // Record suspicious activity if more than 2 accounts
      if (accountCount > 2) {
        await recordSuspiciousActivity(currentUserId, {
          deviceFingerprint,
          activityType: 'multiple_accounts',
          details: {
            accountCount,
            threshold: 2
          }
        });
      }
    }

    return {
      success: true,
      isMultipleAccounts,
      accountCount
    };
  } catch (error) {
    console.error('Error checking multiple accounts on device:', error);
    return {
      success: false,
      isMultipleAccounts: false,
      accountCount: 0,
      error
    };
  }
}

/**
 * Check if an IP address has been used with multiple accounts
 * @param ipAddress IP address
 * @param currentUserId Current user ID
 * @returns Information about multiple account usage
 */
export async function checkMultipleAccountsOnIp(
  ipAddress: string,
  currentUserId: string
): Promise<{
  success: boolean;
  isMultipleAccounts: boolean;
  accountCount: number;
  error?: any;
}> {
  try {
    console.log(`Checking multiple accounts for IP: ${ipAddress}`);

    // Get all users who have used this IP
    const { data, error } = await supabaseAdmin
      .from('device_fingerprints')
      .select('user_id')
      .eq('ip_address', ipAddress);

    if (error) {
      console.error('Error checking multiple accounts on IP:', error);
      throw error;
    }

    // Count unique user IDs
    const uniqueUserIds = new Set(data?.map(item => item.user_id) || []);
    const accountCount = uniqueUserIds.size;

    // Check if this IP has been used with multiple accounts
    const isMultipleAccounts = accountCount > 2; // Allow 2 accounts per IP (could be family members)

    if (isMultipleAccounts) {
      console.log(`IP ${ipAddress} has been used with ${accountCount} accounts`);

      // Record suspicious activity if more than 3 accounts
      if (accountCount > 3) {
        await recordSuspiciousActivity(currentUserId, {
          ipAddress,
          activityType: 'multiple_accounts_ip',
          details: {
            accountCount,
            threshold: 3
          }
        });
      }

      // Update IP usage tracking with account count
      await supabaseAdmin
        .from('ip_usage_tracking')
        .update({ account_count: accountCount })
        .eq('ip_address', ipAddress);
    }

    return {
      success: true,
      isMultipleAccounts,
      accountCount
    };
  } catch (error) {
    console.error('Error checking multiple accounts on IP:', error);
    return {
      success: false,
      isMultipleAccounts: false,
      accountCount: 0,
      error
    };
  }
}

/**
 * Record suspicious activity
 * @param userId User ID
 * @param activityInfo Activity information
 * @returns Success status
 */
export async function recordSuspiciousActivity(
  userId: string,
  activityInfo: {
    deviceFingerprint?: string;
    ipAddress?: string;
    activityType: string;
    details?: any;
  }
): Promise<{ success: boolean; error?: any }> {
  try {
    console.log(`Recording suspicious activity for user ${userId}:`, activityInfo);

    // Insert suspicious activity record
    const { error } = await supabaseAdmin
      .from('suspicious_activity')
      .insert({
        user_id: userId,
        device_fingerprint: activityInfo.deviceFingerprint,
        ip_address: activityInfo.ipAddress,
        activity_type: activityInfo.activityType,
        details: activityInfo.details
      });

    if (error) {
      console.error('Error recording suspicious activity:', error);
      throw error;
    }

    console.log(`Suspicious activity recorded successfully for user: ${userId}`);
    return { success: true };
  } catch (error) {
    console.error('Error recording suspicious activity:', error);
    return { success: false, error };
  }
}

/**
 * Check device usage limit
 * @param deviceFingerprint Device fingerprint
 * @returns Whether the device has reached its usage limit
 */
export async function checkDeviceUsageLimit(
  deviceFingerprint: string
): Promise<{
  success: boolean;
  hasReachedLimit: boolean;
  requestCount: number;
  error?: any;
}> {
  try {
    console.log(`Checking device usage limit for: ${deviceFingerprint}`);

    if (!deviceFingerprint) {
      return { success: true, hasReachedLimit: false, requestCount: 0 };
    }

    // Get total requests for this device
    const { data, error } = await supabaseAdmin
      .from('device_fingerprints')
      .select('request_count')
      .eq('fingerprint', deviceFingerprint);

    if (error) {
      console.error('Error checking device usage limit:', error);
      throw error;
    }

    // Calculate total requests
    const totalRequests = data?.reduce((sum, item) => sum + (item.request_count || 0), 0) || 0;

    // Check if device has reached limit (30 requests per device for free tier)
    const hasReachedLimit = totalRequests >= 30;

    if (hasReachedLimit) {
      console.log(`Device ${deviceFingerprint} has reached usage limit: ${totalRequests} requests`);
    }

    return {
      success: true,
      hasReachedLimit,
      requestCount: totalRequests
    };
  } catch (error) {
    console.error('Error checking device usage limit:', error);
    return {
      success: false,
      hasReachedLimit: false,
      requestCount: 0,
      error
    };
  }
}

/**
 * Check IP address usage limit
 * @param ipAddress IP address
 * @returns Whether the IP has reached its usage limit
 */
export async function checkIpUsageLimit(
  ipAddress: string
): Promise<{
  success: boolean;
  hasReachedLimit: boolean;
  requestCount: number;
  accountCount: number;
  error?: any;
}> {
  try {
    console.log(`Checking IP usage limit for: ${ipAddress}`);

    if (!ipAddress) {
      return { success: true, hasReachedLimit: false, requestCount: 0, accountCount: 0 };
    }

    // Get IP usage data
    const { data, error } = await supabaseAdmin
      .from('ip_usage_tracking')
      .select('request_count, account_count')
      .eq('ip_address', ipAddress)
      .single();

    if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
      console.error('Error checking IP usage limit:', error);
      throw error;
    }

    const requestCount = data?.request_count || 0;
    const accountCount = data?.account_count || 0;

    // Check if IP has reached limit (50 requests per IP for free tier)
    // or if too many accounts have been created from this IP
    const hasReachedLimit = requestCount >= 50 || accountCount > 5;

    if (hasReachedLimit) {
      console.log(`IP ${ipAddress} has reached usage limit: ${requestCount} requests, ${accountCount} accounts`);
    }

    return {
      success: true,
      hasReachedLimit,
      requestCount,
      accountCount
    };
  } catch (error) {
    console.error('Error checking IP usage limit:', error);
    return {
      success: false,
      hasReachedLimit: false,
      requestCount: 0,
      accountCount: 0,
      error
    };
  }
}
