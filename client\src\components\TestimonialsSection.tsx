import { motion } from 'framer-motion';
import { TESTIMONIALS } from '@/lib/constants';
import { CyberpunkSeparator } from './ui/separator';
import { User } from 'lucide-react';

function StarRating({ rating }: { rating: number }) {
  const fullStars = Math.floor(rating);
  const hasHalfStar = rating % 1 !== 0;
  const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);

  return (
    <div className="flex text-accent">
      {[...Array(fullStars)].map((_, i) => (
        <svg key={`full-${i}`} xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="currentColor" stroke="none">
          <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2" />
        </svg>
      ))}

      {hasHalfStar && (
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="currentColor" stroke="none">
          <path d="M12 2L15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2" />
          <path d="M12 2L12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2" fill="none" stroke="currentColor" strokeWidth="2" />
        </svg>
      )}

      {[...Array(emptyStars)].map((_, i) => (
        <svg key={`empty-${i}`} xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2" />
        </svg>
      ))}
    </div>
  );
}

export default function TestimonialsSection() {
  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0, transition: { duration: 0.5 } }
  };

  return (
    <section id="testimonials" className="py-16 relative">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="text-center mb-16"
        >
          <h2 className="font-heading text-4xl font-bold">
            <span className="text-primary neon-text">Testimonials</span> (All Suspiciously Positive)
          </h2>
          <p className="text-muted-foreground mt-4 max-w-2xl mx-auto">
            Don't take our word for it. Take these clearly real and not-at-all fabricated testimonials from our "users."
          </p>
        </motion.div>

        <motion.div
          className="grid md:grid-cols-3 gap-8"
          variants={container}
          initial="hidden"
          whileInView="show"
          viewport={{ once: true, amount: 0.2 }}
        >
          {TESTIMONIALS.map((testimonial) => (
            <motion.div
              key={testimonial.id}
              className="bg-card border border-muted rounded-lg p-6 relative"
              variants={item}
            >
              <div className="absolute top-0 right-0 transform translate-x-1/4 -translate-y-1/4">
                <div className="text-primary text-5xl">"</div>
              </div>

              <div className="flex space-x-4 items-center mb-4">
                <div className="w-12 h-12 bg-background rounded-full flex items-center justify-center">
                  <User className="text-primary" />
                </div>
                <div>
                  <div className="font-heading">{testimonial.name}</div>
                  <div className="text-muted-foreground text-sm">{testimonial.title}</div>
                </div>
              </div>

              <blockquote className="text-muted-foreground mb-4">
                {testimonial.text}
              </blockquote>

              <StarRating rating={testimonial.rating} />
            </motion.div>
          ))}
        </motion.div>

        <div className="text-center mt-10">
          <a
            href="#"
            className="inline-block px-6 py-3 bg-card text-muted-foreground border border-muted rounded-md hover:border-primary transition"
          >
            See More Suspiciously Positive Reviews
          </a>
        </div>

        <CyberpunkSeparator className="my-20" />
    </section>
  );
}
