import { useState, useEffect, createContext, useContext } from 'react';
import { X } from 'lucide-react';
import { cn } from '@/lib/utils';

type ToastVariant = 'default' | 'destructive' | 'success';

interface Toast {
  id: string;
  title?: string;
  description?: string;
  variant?: ToastVariant;
}

interface ToastContextType {
  toast: (props: Omit<Toast, 'id'>) => void;
  dismiss: (id: string) => void;
}

const ToastContext = createContext<ToastContextType>({
  toast: () => {},
  dismiss: () => {},
});

export const useToast = () => useContext(ToastContext);

interface ToastProviderProps {
  children: React.ReactNode;
}

export function ToastProvider({ children }: ToastProviderProps) {
  const [toasts, setToasts] = useState<Toast[]>([]);

  const toast = (props: Omit<Toast, 'id'>) => {
    const id = Math.random().toString(36).substring(2, 9);
    setToasts((prev) => [...prev, { id, ...props }]);
    
    // Auto dismiss after 5 seconds
    setTimeout(() => {
      dismiss(id);
    }, 5000);
  };

  const dismiss = (id: string) => {
    setToasts((prev) => prev.filter((toast) => toast.id !== id));
  };

  return (
    <ToastContext.Provider value={{ toast, dismiss }}>
      {children}
      <div className="fixed bottom-0 right-0 z-50 p-4 space-y-2 max-w-md w-full">
        {toasts.map((toast) => (
          <div
            key={toast.id}
            className={cn(
              "p-4 rounded-lg shadow-lg border transition-all duration-300 transform translate-y-0 opacity-100",
              "animate-in slide-in-from-right-full",
              toast.variant === 'destructive' 
                ? "bg-red-50 dark:bg-red-950 border-red-200 dark:border-red-800" 
                : toast.variant === 'success'
                ? "bg-green-50 dark:bg-green-950 border-green-200 dark:border-green-800"
                : "bg-background border-border"
            )}
          >
            <div className="flex justify-between items-start">
              <div>
                {toast.title && (
                  <h3 className={cn(
                    "font-medium text-sm",
                    toast.variant === 'destructive' 
                      ? "text-red-800 dark:text-red-300" 
                      : toast.variant === 'success'
                      ? "text-green-800 dark:text-green-300"
                      : "text-foreground"
                  )}>
                    {toast.title}
                  </h3>
                )}
                {toast.description && (
                  <p className="text-sm text-muted-foreground mt-1">
                    {toast.description}
                  </p>
                )}
              </div>
              <button
                onClick={() => dismiss(toast.id)}
                className="text-muted-foreground hover:text-foreground"
              >
                <X size={16} />
              </button>
            </div>
          </div>
        ))}
      </div>
    </ToastContext.Provider>
  );
}
