import { useState, useEffect } from 'react';
import { Menu } from 'lucide-react';
import { But<PERSON> } from "@/components/ui/button";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { ThemeToggle } from "@/components/ThemeToggle";
import { Link } from "wouter";

const navLinks = [
  { href: "/features", label: "Features" },
  { href: "/integration", label: "Integration" },
  { href: "/contact", label: "Contact" }
];

export default function Header() {
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    // Initial check
    handleScroll();

    window.addEventListener('scroll', handleScroll);

    // Clean up
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  // Check if we're in light mode by looking at the document element
  const documentIsLight = () => {
    if (typeof document !== 'undefined') {
      return document.documentElement.classList.contains('light');
    }
    return false;
  };

  return (
    <nav className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300
      ${isScrolled
        ? documentIsLight()
          ? 'bg-white backdrop-blur-md border-b border-border/50 py-2 md:py-3 shadow-lg'
          : 'bg-black/95 backdrop-blur-md border-b border-border/50 py-2 md:py-3 shadow-lg'
        : 'bg-transparent py-3 md:py-4'}`}>
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 xl:px-10 max-w-[1400px]">
        <div className="flex justify-between items-center">
          {/* Logo */}
          <div className="flex items-center space-x-3">
            <Link href="/">
              <div className="flex items-center space-x-3 cursor-pointer">
                <div className="h-10 w-10 rounded-full flex items-center justify-center shadow-lg transform transition-transform duration-300 hover:scale-110 overflow-hidden">
                  <img src="/images/browzy-logo.png" alt="Browzy Logo" className="h-full w-full object-cover" />
                </div>
                <span className={`font-inter font-bold text-2xl ${isScrolled
                  ? (documentIsLight() ? 'text-foreground' : 'text-white')
                  : 'text-foreground'} bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary`}>
                  browzy
                </span>
              </div>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center">
            <div className="mr-4 lg:mr-6 xl:mr-10 flex gap-4 lg:gap-6 xl:gap-10">
              {navLinks.map((link) => (
                link.href.startsWith('/') ? (
                  <Link
                    key={link.href}
                    href={link.href}
                    className={`${isScrolled
                      ? (documentIsLight() ? 'text-foreground' : 'text-white')
                      : 'text-foreground'} hover:text-primary transition-all duration-300 font-medium text-base relative group transform hover:scale-105 font-inter`}
                  >
                    {link.label}
                    <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-full"></span>
                  </Link>
                ) : (
                  <a
                    key={link.href}
                    href={link.href}
                    className={`${isScrolled
                      ? (documentIsLight() ? 'text-foreground' : 'text-white')
                      : 'text-foreground'} hover:text-primary transition-all duration-300 font-medium text-base relative group transform hover:scale-105 font-inter`}
                  >
                    {link.label}
                    <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-full"></span>
                  </a>
                )
              ))}
            </div>

            <div className="flex items-center gap-3 md:gap-4">
              <ThemeToggle />
            </div>
          </div>

          {/* Mobile Menu */}
          <div className="md:hidden">
            <Sheet>
              <SheetTrigger asChild>
                <Button variant="ghost" size="icon" className={`${isScrolled
                  ? (documentIsLight() ? 'text-foreground' : 'text-white')
                  : 'text-foreground'} hover:text-primary transition`}>
                  <Menu size={24} />
                </Button>
              </SheetTrigger>
              <SheetContent side="right" className="bg-background/95 backdrop-blur-md border-l border-border/50">
                <div className="flex flex-col space-y-6 mt-10">
                  {navLinks.map((link) => (
                    link.href.startsWith('/') ? (
                      <Link
                        key={link.href}
                        href={link.href}
                        className="text-foreground hover:text-primary transition font-medium py-3 px-4 border-b border-border/30 font-inter"
                      >
                        {link.label}
                      </Link>
                    ) : (
                      <a
                        key={link.href}
                        href={link.href}
                        className="text-foreground hover:text-primary transition font-medium py-3 px-4 border-b border-border/30 font-inter"
                      >
                        {link.label}
                      </a>
                    )
                  ))}
                  <div className="flex items-center justify-between py-3 px-4 border-b border-border/30">
                    <span className="font-medium font-inter">Theme</span>
                    <ThemeToggle />
                  </div>
                  <div className="flex flex-col gap-4 pt-6 px-4">
                    {/* Mobile menu content can be added here if needed */}
                  </div>
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>
    </nav>
  );
}
