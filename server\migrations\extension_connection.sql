-- Create extension_tokens table
CREATE TABLE IF NOT EXISTS extension_tokens (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  token VARCHAR(20) NOT NULL UNIQUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_used TIMESTAMP WITH TIME ZONE,
  CONSTRAINT token_length CHECK (char_length(token) = 9)
);

-- Create index on user_id for faster lookups
CREATE INDEX IF NOT EXISTS idx_extension_tokens_user_id ON extension_tokens(user_id);

-- Create index on token for faster validation
CREATE INDEX IF NOT EXISTS idx_extension_tokens_token ON extension_tokens(token);

-- Create chat_history table
CREATE TABLE IF NOT EXISTS chat_history (
  id VARCHAR(100) PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  sender VARCHAR(10) NOT NULL CHECK (sender IN ('user', 'ai')),
  timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  url TEXT,
  CONSTRAINT content_not_empty CHECK (char_length(content) > 0)
);

-- Create index on user_id for faster lookups
CREATE INDEX IF NOT EXISTS idx_chat_history_user_id ON chat_history(user_id);

-- Create index on timestamp for sorting
CREATE INDEX IF NOT EXISTS idx_chat_history_timestamp ON chat_history(timestamp);

-- Create RLS policies for extension_tokens

-- Enable RLS on extension_tokens
ALTER TABLE extension_tokens ENABLE ROW LEVEL SECURITY;

-- Create policy to allow users to see only their own tokens
CREATE POLICY select_own_tokens ON extension_tokens
  FOR SELECT USING (auth.uid() = user_id);

-- Create policy to allow users to insert their own tokens
CREATE POLICY insert_own_tokens ON extension_tokens
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Create policy to allow users to update their own tokens
CREATE POLICY update_own_tokens ON extension_tokens
  FOR UPDATE USING (auth.uid() = user_id);

-- Create policy to allow users to delete their own tokens
CREATE POLICY delete_own_tokens ON extension_tokens
  FOR DELETE USING (auth.uid() = user_id);

-- Create RLS policies for chat_history

-- Enable RLS on chat_history
ALTER TABLE chat_history ENABLE ROW LEVEL SECURITY;

-- Create policy to allow users to see only their own chat history
CREATE POLICY select_own_chat_history ON chat_history
  FOR SELECT USING (auth.uid() = user_id);

-- Create policy to allow users to insert their own chat history
CREATE POLICY insert_own_chat_history ON chat_history
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Create policy to allow users to update their own chat history
CREATE POLICY update_own_chat_history ON chat_history
  FOR UPDATE USING (auth.uid() = user_id);

-- Create policy to allow users to delete their own chat history
CREATE POLICY delete_own_chat_history ON chat_history
  FOR DELETE USING (auth.uid() = user_id);
