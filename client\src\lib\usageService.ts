/**
 * Interface for usage summary
 */
export interface UsageSummary {
  daily: {
    total: number;
    limit: number;
    remaining: number;
    resetsAt?: string; // ISO string of when the daily limit resets
  };
  monthly: {
    total: number;
    limit: number;
    remaining: number;
  };
  plan: {
    id: string;
    name: string;
    totalLimit: number;
    monthlyLimit: number;
    dailyLimit?: number;
  };
}

/**
 * Get the user's usage summary
 * @returns The user's usage summary
 */
export async function getUserUsageSummary(): Promise<{
  success: boolean;
  summary?: UsageSummary;
  error?: string
}> {
  try {
    const response = await fetch('/api/usage/summary', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to get usage summary');
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error getting usage summary:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Record usage for a specific request type
 * @param requestType The type of request (chat, search, etc.)
 * @param requestCount The number of requests to record (default: 1)
 * @returns Success status
 */
export async function recordUsage(
  requestType: string,
  requestCount: number = 1
): Promise<{
  success: boolean;
  limitReached?: boolean;
  error?: string
}> {
  try {
    const response = await fetch('/api/usage/record', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
      body: JSON.stringify({ requestType, requestCount }),
    });

    if (!response.ok) {
      const errorData = await response.json();

      // Check if the error is due to reaching the usage limit
      if (response.status === 403 && errorData.limitReached) {
        return {
          success: false,
          limitReached: true,
          error: 'Usage limit reached'
        };
      }

      throw new Error(errorData.error || 'Failed to record usage');
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error recording usage:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Check if the user has reached their usage limit
 * @returns Whether the user has reached their limit
 */
export async function checkUsageLimit(): Promise<{
  success: boolean;
  hasReachedLimit?: boolean;
  error?: string
}> {
  try {
    const response = await fetch('/api/usage/check-limit', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to check usage limit');
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error checking usage limit:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Format the usage summary for display
 * @param summary The usage summary
 * @returns Formatted usage data for display
 */
export function formatUsageSummary(summary: UsageSummary) {
  const isFree = summary.plan.id === 'free';

  // Format the reset time if available
  let resetTimeFormatted = '';
  if (summary.daily.resetsAt) {
    try {
      const resetDate = new Date(summary.daily.resetsAt);
      resetTimeFormatted = resetDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } catch (error) {
      console.error('Error formatting reset time:', error);
      resetTimeFormatted = 'midnight';
    }
  } else {
    resetTimeFormatted = 'midnight';
  }

  return {
    daily: {
      total: summary.daily.total,
      limit: summary.daily.limit === -1 ? 'Unlimited' : summary.daily.limit,
      remaining: summary.daily.limit === -1 ? 'Unlimited' : summary.daily.remaining,
      percentage: summary.daily.limit === -1 ? 0 : Math.min(100, Math.round((summary.daily.total / summary.daily.limit) * 100)),
      resetsAt: summary.daily.resetsAt,
      resetTimeFormatted
    },
    monthly: {
      total: summary.monthly.total,
      limit: summary.monthly.limit === -1 ? 'Unlimited' : summary.monthly.limit,
      remaining: summary.monthly.limit === -1 ? 'Unlimited' : summary.monthly.remaining,
      percentage: summary.monthly.limit === -1 ? 0 : Math.min(100, Math.round((summary.monthly.total / summary.monthly.limit) * 100)),
    },
    plan: {
      id: summary.plan.id,
      name: summary.plan.name,
      totalLimit: summary.plan.totalLimit === -1 ? 'Unlimited' : summary.plan.totalLimit,
      monthlyLimit: summary.plan.monthlyLimit === -1 ? 'Unlimited' : summary.plan.monthlyLimit,
      dailyLimit: summary.plan.dailyLimit === -1 ? 'Unlimited' : summary.plan.dailyLimit,
      isFree: isFree
    }
  };
}
