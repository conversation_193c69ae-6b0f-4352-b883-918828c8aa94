import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Loader2 } from 'lucide-react';

interface ApiStatus {
  status: string;
  message: string;
  timestamp: string;
  environment: string;
}

export function ApiTest() {
  const [status, setStatus] = useState<ApiStatus | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const checkApiStatus = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/status');
      
      if (!response.ok) {
        throw new Error(`API returned status ${response.status}`);
      }
      
      const data = await response.json();
      setStatus(data);
    } catch (err: any) {
      console.error('Error checking API status:', err);
      setError(err.message || 'Failed to connect to the API');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // Check API status on component mount
    checkApiStatus();
  }, []);

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle>API Connection Test</CardTitle>
        <CardDescription>
          Check if the frontend is properly connected to the backend server
        </CardDescription>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex justify-center items-center py-6">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : error ? (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4">
            <p className="text-red-600 dark:text-red-400 font-medium">Connection Error</p>
            <p className="text-red-500 dark:text-red-300 text-sm mt-1">{error}</p>
          </div>
        ) : status ? (
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="font-medium">Status:</span>
              <Badge variant={status.status === 'online' ? 'default' : 'destructive'}>
                {status.status}
              </Badge>
            </div>
            <div>
              <span className="font-medium">Message:</span>
              <p className="text-muted-foreground mt-1">{status.message}</p>
            </div>
            <div>
              <span className="font-medium">Environment:</span>
              <p className="text-muted-foreground mt-1">{status.environment}</p>
            </div>
            <div>
              <span className="font-medium">Last Checked:</span>
              <p className="text-muted-foreground mt-1">
                {new Date(status.timestamp).toLocaleString()}
              </p>
            </div>
          </div>
        ) : (
          <p className="text-center text-muted-foreground py-6">No status information available</p>
        )}
      </CardContent>
      <CardFooter>
        <Button 
          onClick={checkApiStatus} 
          disabled={loading}
          className="w-full"
        >
          {loading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Checking...
            </>
          ) : (
            'Check API Connection'
          )}
        </Button>
      </CardFooter>
    </Card>
  );
}
