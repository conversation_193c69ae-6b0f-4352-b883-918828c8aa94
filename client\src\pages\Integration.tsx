import { useEffect } from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import IntegrationSection from '@/components/IntegrationSection';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { ArrowRight, Puzzle } from 'lucide-react';

export default function Integration() {
  useEffect(() => {
    // Update document title
    document.title = "Integration - browzy - Supercharge Your Browser with AI";

    // Add meta description
    const metaDescription = document.createElement('meta');
    metaDescription.name = 'description';
    metaDescription.content = 'Learn how to integrate and set up the browzy extension with your account and API keys. Simple steps to get started with browzy.';
    document.head.appendChild(metaDescription);

    window.scrollTo(0, 0);

    return () => {
      document.head.removeChild(metaDescription);
    };
  }, []);

  return (
    <div className="min-h-screen dark:bg-black light:bg-white text-foreground overflow-hidden">
      <Header />

      <div className="max-w-6xl mx-auto px-6 sm:px-8 lg:px-12 pt-28 md:pt-32">
        <main className="py-8 md:py-12">
          <section className="py-12 md:py-16 relative">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="text-center mb-16"
            >
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.7, delay: 0.2 }}
                className="inline-block mb-3 px-5 py-2 bg-primary/10 rounded-full text-primary text-sm font-medium border border-primary/20"
              >
                <Puzzle className="w-4 h-4 inline-block mr-2" />
                Setup Guide
              </motion.div>

              <h1 className="font-inter text-5xl md:text-6xl font-bold tracking-tight">
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary">Integration</span> Guide
              </h1>
              <p className="font-inter text-lg md:text-xl text-muted-foreground mt-6 max-w-2xl mx-auto leading-relaxed">
                Everything you need to know about setting up and connecting the browzy extension with your account and preferred AI provider.
              </p>
            </motion.div>

            <IntegrationSection />

            <motion.div
              className="text-center mt-16"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <h2 className="font-inter text-3xl md:text-4xl font-bold mb-8">
                Need More Help?
              </h2>

              <p className="text-muted-foreground mb-8 max-w-2xl mx-auto font-inter">
                If you're experiencing any issues with the integration or have questions, our support team is here to help.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <div className="relative group">
                  <div className="absolute -inset-0.5 bg-gradient-to-r from-primary to-secondary rounded-xl blur opacity-70 group-hover:opacity-100 transition duration-500 group-hover:duration-200 animate-pulse-slow"></div>
                  <Button
                    className="relative w-full sm:w-auto bg-gradient-to-r from-primary to-secondary text-white shadow-xl text-sm sm:text-base py-4 sm:py-5 px-6 sm:px-8 rounded-lg sm:rounded-xl transition-all duration-300 transform hover:scale-105"
                    size="lg"
                    asChild
                  >
                    <a href="/contact">
                      Contact Support
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </a>
                  </Button>
                </div>

                <div className="relative group">
                  <div className="absolute -inset-0.5 bg-gradient-to-r from-secondary/50 to-secondary/30 rounded-xl blur opacity-30 group-hover:opacity-70 transition duration-500 group-hover:duration-200"></div>
                  <Button
                    variant="outline"
                    className="relative w-full sm:w-auto border-2 border-secondary text-secondary hover:bg-secondary/10 text-sm sm:text-base py-4 sm:py-5 px-6 sm:px-8 rounded-lg sm:rounded-xl transition-all duration-300 transform hover:scale-105"
                    size="lg"
                    asChild
                  >
                    <a href="https://docs.browzy.ai" target="_blank" rel="noopener noreferrer">
                      View Documentation
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </a>
                  </Button>
                </div>
              </div>
            </motion.div>
          </section>
        </main>
        <Footer />
      </div>
    </div>
  );
}
