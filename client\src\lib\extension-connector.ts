import { nanoid } from 'nanoid';

// Connection status listener type
type ConnectionListener = (status: { isConnected: boolean, username: string | null }) => void;

// Token data type
interface TokenData {
  token: string;
  createdAt: number;
  expiresAt: number;
}

// User data type
interface UserData {
  userId: string;
  email: string | undefined;
  username: string;
}

class ExtensionConnector {
  private static instance: ExtensionConnector;
  private connectionListeners: ConnectionListener[] = [];
  private isConnected: boolean = false;
  private connectedUsername: string | null = null;
  private currentToken: TokenData | null = null;
  private tokenRefreshInterval: number | null = null;
  private readonly TOKEN_LENGTH = 9;
  private readonly TOKEN_EXPIRY_HOURS = 24;
  private readonly TOKEN_STORAGE_KEY = 'browzy_connection_token';
  private readonly CONNECTION_STATUS_KEY = 'browzy_connection_status';

  private constructor() {
    // Load any existing token from storage
    this.loadTokenFromStorage();

    // Check if there's an existing connection
    this.loadConnectionStatus();

    // Set up token refresh interval
    this.setupTokenRefreshInterval();

    // Listen for messages from the extension
    window.addEventListener('message', this.handleExtensionMessage.bind(this));
  }

  public static getInstance(): ExtensionConnector {
    if (!ExtensionConnector.instance) {
      ExtensionConnector.instance = new ExtensionConnector();
    }
    return ExtensionConnector.instance;
  }

  /**
   * Generate a new connection token
   * @returns The generated token data
   */
  public generateToken(): TokenData {
    // Generate a random 9-character alphanumeric token
    const token = nanoid(this.TOKEN_LENGTH);

    // Set expiry time (24 hours from now)
    const now = Date.now();
    const expiresAt = now + (this.TOKEN_EXPIRY_HOURS * 60 * 60 * 1000);

    // Store the token data
    this.currentToken = {
      token,
      createdAt: now,
      expiresAt
    };

    // Save to storage
    this.saveTokenToStorage();

    return this.currentToken;
  }

  /**
   * Get the current token data
   * @returns The current token data or null if no token exists
   */
  public getCurrentToken(): TokenData | null {
    // Check if token exists and is valid
    if (this.currentToken && this.isTokenValid(this.currentToken)) {
      return this.currentToken;
    }

    // If no valid token exists, generate a new one
    return this.generateToken();
  }

  /**
   * Check if a token is valid (not expired)
   * @param tokenData The token data to check
   * @returns True if the token is valid, false otherwise
   */
  private isTokenValid(tokenData: TokenData): boolean {
    return Date.now() < tokenData.expiresAt;
  }

  /**
   * Save the current token to storage
   */
  private saveTokenToStorage(): void {
    if (this.currentToken) {
      localStorage.setItem(this.TOKEN_STORAGE_KEY, JSON.stringify(this.currentToken));
    }
  }

  /**
   * Load a token from storage
   */
  private loadTokenFromStorage(): void {
    const storedToken = localStorage.getItem(this.TOKEN_STORAGE_KEY);
    if (storedToken) {
      try {
        const tokenData = JSON.parse(storedToken) as TokenData;
        if (this.isTokenValid(tokenData)) {
          this.currentToken = tokenData;
        } else {
          // Token is expired, remove it
          localStorage.removeItem(this.TOKEN_STORAGE_KEY);
        }
      } catch (error) {
        console.error('Error parsing stored token:', error);
        localStorage.removeItem(this.TOKEN_STORAGE_KEY);
      }
    }
  }

  /**
   * Save the current connection status to storage
   */
  private saveConnectionStatus(): void {
    localStorage.setItem(this.CONNECTION_STATUS_KEY, JSON.stringify({
      isConnected: this.isConnected,
      username: this.connectedUsername
    }));
  }

  /**
   * Load connection status from storage
   */
  private loadConnectionStatus(): void {
    const storedStatus = localStorage.getItem(this.CONNECTION_STATUS_KEY);
    if (storedStatus) {
      try {
        const status = JSON.parse(storedStatus);
        this.isConnected = status.isConnected || false;
        this.connectedUsername = status.username || null;
      } catch (error) {
        console.error('Error parsing stored connection status:', error);
        localStorage.removeItem(this.CONNECTION_STATUS_KEY);
      }
    }
  }

  /**
   * Set up token refresh interval
   */
  private setupTokenRefreshInterval(): void {
    // Clear any existing interval
    if (this.tokenRefreshInterval) {
      window.clearInterval(this.tokenRefreshInterval);
    }

    // Check token validity every hour
    this.tokenRefreshInterval = window.setInterval(() => {
      if (this.currentToken && !this.isTokenValid(this.currentToken)) {
        // Token is expired, generate a new one
        this.generateToken();

        // Notify listeners of the change
        this.notifyListeners();
      }
    }, 60 * 60 * 1000); // 1 hour
  }

  /**
   * Handle messages from the extension
   * @param event The message event
   */
  private handleExtensionMessage(event: MessageEvent): void {
    // Ensure the message is from our extension
    if (event.data && event.data.source === 'browzy_extension') {
      switch (event.data.action) {
        case 'connect':
          // Extension is trying to connect with a token
          this.validateAndConnect(event.data.token, event.data.userData);
          break;

        case 'disconnect':
          // Extension is disconnecting
          this.disconnectExtension();
          break;

        case 'ping':
          // Extension is checking connection status
          this.sendMessageToExtension('pong', { isConnected: this.isConnected });
          break;

        case 'update_data':
          // Extension is sending updated data
          // This would be handled by specific data handlers
          console.log('Received data update from extension:', event.data.data);
          break;
      }
    }
  }

  /**
   * Validate a token and connect if valid
   * @param token The token to validate
   * @param userData The user data from the extension
   */
  private validateAndConnect(token: string, userData: any): void {
    // Check if we have a valid token
    if (this.currentToken && this.currentToken.token === token && this.isTokenValid(this.currentToken)) {
      // Token is valid, establish connection
      this.isConnected = true;
      this.connectedUsername = userData?.username || 'User';

      // Save connection status
      this.saveConnectionStatus();

      // Send confirmation to extension
      this.sendMessageToExtension('connection_success', {
        message: 'Connected successfully',
        userData: {
          username: this.connectedUsername
        }
      });

      // Notify listeners
      this.notifyListeners();
    } else {
      // Token is invalid
      this.sendMessageToExtension('connection_error', {
        message: 'Invalid or expired token'
      });
    }
  }

  /**
   * Send a message to the extension
   * @param action The action to perform
   * @param data The data to send
   */
  private sendMessageToExtension(action: string, data: any): void {
    window.postMessage({
      source: 'browzy_app',
      action,
      data
    }, '*');
  }

  /**
   * Check if the extension is available
   * @returns True if the extension is available, false otherwise
   */
  public isExtensionAvailable(): boolean {
    // This is a simple check that will be improved when we implement the actual extension detection
    // For now, we'll assume the extension is available in development
    return true;
  }

  /**
   * Connect to the extension
   * @param userData The user data to send to the extension
   */
  public connectExtension(userData: UserData): void {
    // Ensure we have a valid token
    const tokenData = this.getCurrentToken();
    
    if (!tokenData) {
      console.error('Failed to generate token');
      return;
    }

    // Send connection request to extension
    this.sendMessageToExtension('connect_request', {
      token: tokenData.token,
      userData
    });
  }

  /**
   * Disconnect from the extension
   */
  public disconnectExtension(): void {
    // Send disconnect request to extension
    this.sendMessageToExtension('disconnect_request', {});

    // Update local state
    this.isConnected = false;
    this.connectedUsername = null;

    // Save connection status
    this.saveConnectionStatus();

    // Notify listeners
    this.notifyListeners();
  }

  /**
   * Check the current connection status
   */
  public checkConnectionStatus(): void {
    // Send ping to extension
    this.sendMessageToExtension('ping', {});

    // Notify listeners of current status
    this.notifyListeners();
  }

  /**
   * Add a connection listener
   * @param listener The listener to add
   */
  public addConnectionListener(listener: ConnectionListener): void {
    this.connectionListeners.push(listener);
  }

  /**
   * Remove a connection listener
   * @param listener The listener to remove
   */
  public removeConnectionListener(listener: ConnectionListener): void {
    this.connectionListeners = this.connectionListeners.filter(l => l !== listener);
  }

  /**
   * Notify all listeners of the current connection status
   */
  private notifyListeners(): void {
    const status = {
      isConnected: this.isConnected,
      username: this.connectedUsername
    };

    this.connectionListeners.forEach(listener => {
      try {
        listener(status);
      } catch (error) {
        console.error('Error in connection listener:', error);
      }
    });
  }

  /**
   * Disconnect and clean up
   */
  public dispose(): void {
    // Clear interval
    if (this.tokenRefreshInterval) {
      window.clearInterval(this.tokenRefreshInterval);
    }

    // Remove event listener
    window.removeEventListener('message', this.handleExtensionMessage);

    // Clear listeners
    this.connectionListeners = [];
  }
}

// Export a singleton instance
export const extensionConnector = ExtensionConnector.getInstance();
