import { motion } from 'framer-motion';
import { FlamethrowerButton } from './ui/flamethrower';
import { ArrowR<PERSON>, Sparkles, AlertTriangle } from 'lucide-react';

export default function DownloadCTA() {
  return (
    <section id="download" className="py-28 md:py-32 relative bg-muted overflow-hidden">
      {/* Enhanced background effects */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* Primary glow */}
        <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-[90%] h-[70%] bg-primary/15 rounded-full blur-[180px] animate-pulse-slow"></div>

        {/* Secondary glow */}
        <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-[80%] h-[60%] bg-secondary/15 rounded-full blur-[160px] animate-pulse-slow-reverse"></div>

        {/* Accent glows */}
        <div className="absolute top-1/4 left-1/4 w-[40%] h-[40%] bg-accent/10 rounded-full blur-[120px] animate-float"></div>
        <div className="absolute bottom-1/3 right-1/4 w-[30%] h-[30%] bg-primary/10 rounded-full blur-[100px] animate-float opacity-70"></div>
      </div>

      {/* Grid pattern overlay */}
      <div className="absolute inset-0 grid-pattern opacity-10"></div>

      {/* Noise texture overlay */}
      <div className="absolute inset-0 noise-bg opacity-[0.03] mix-blend-overlay pointer-events-none"></div>

      {/* Floating elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-[15%] left-[10%] w-16 h-16 bg-primary/20 rounded-full blur-lg animate-float"></div>
        <div className="absolute top-[25%] right-[15%] w-20 h-20 bg-secondary/20 rounded-full blur-lg animate-float-slow"></div>
        <div className="absolute bottom-[20%] left-[20%] w-12 h-12 bg-accent/20 rounded-full blur-md animate-float-reverse"></div>
        <div className="absolute bottom-[30%] right-[25%] w-10 h-10 bg-primary/20 rounded-full blur-md animate-float-slow-reverse"></div>
      </div>

      <div className="container mx-auto px-4 sm:px-6 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="max-w-4xl mx-auto text-center"
        >
          {/* Badge */}
          <motion.div
            className="inline-flex items-center px-6 py-3 rounded-full bg-primary/15 text-primary mb-10 text-base font-medium border border-primary/20 shadow-xl font-inter"
            initial={{ opacity: 0, y: -20 }}
            animate={{
              opacity: 1,
              y: 0,
              boxShadow: [
                "0 10px 25px rgba(var(--primary), 0.2)",
                "0 10px 35px rgba(var(--primary), 0.4)",
                "0 10px 25px rgba(var(--primary), 0.2)"
              ],
              transition: {
                boxShadow: {
                  repeat: Infinity,
                  duration: 2
                }
              }
            }}
            transition={{ duration: 0.5 }}
            whileHover={{ scale: 1.05, backgroundColor: "rgba(var(--primary), 0.2)" }}
          >
            <Sparkles className="mr-2 h-5 w-5" />
            <span className="font-semibold tracking-wide">Ready to Get Started</span>
            <div className="ml-3 w-3 h-3 rounded-full bg-primary animate-pulse"></div>
          </motion.div>

          <div className="relative">
            <motion.div
              className="absolute -inset-10 bg-gradient-to-r from-primary/5 via-secondary/10 to-primary/5 rounded-full blur-3xl -z-10 opacity-60"
              animate={{
                scale: [1, 1.05, 1],
                opacity: [0.5, 0.7, 0.5]
              }}
              transition={{
                duration: 8,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />

            <h2 className="font-inter text-6xl md:text-7xl font-bold leading-tight tracking-tight">
              <motion.div
                className="overflow-hidden"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.5 }}
              >
                <motion.span
                  className="block mb-6"
                  initial={{ y: 40 }}
                  animate={{ y: 0 }}
                  transition={{ duration: 0.7, delay: 0.1 }}
                >
                  Ready to Add Some
                </motion.span>
              </motion.div>

              <motion.div className="flex items-center justify-center gap-4 flex-wrap">
                <motion.span
                  className="relative inline-block"
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.7, delay: 0.3 }}
                >
                  <span className="bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary relative z-10">
                    browzy
                  </span>
                  <div className="absolute -inset-2 bg-gradient-to-r from-primary/20 to-secondary/20 blur-xl rounded-full -z-10 opacity-70"></div>
                </motion.span>

                <motion.span
                  className="text-secondary relative inline-block"
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.7, delay: 0.5 }}
                >
                  to Your Browser?
                  <div className="absolute -inset-2 bg-gradient-to-r from-secondary/20 to-transparent blur-xl rounded-full -z-10 opacity-50"></div>
                </motion.span>
              </motion.div>
            </h2>
          </div>

          <motion.p
            className="text-muted-foreground mt-10 text-xl md:text-2xl font-inter leading-relaxed max-w-2xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.6 }}
          >
            Join thousands of users who've embraced digital judgment with our AI-powered browser extension.
            <span className="block mt-2 text-foreground font-medium">Available for <span className="text-primary">Chrome</span> and <span className="text-orange-500">Brave</span> browsers.</span>
            <span className="block mt-2 text-secondary italic font-medium">It's free, which is suspiciously generous of us.</span>
          </motion.p>

          {/* Browser illustrations */}
          <motion.div
            className="mt-12 mb-16 relative max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.7 }}
          >
            <div className="absolute inset-0 bg-gradient-to-r from-primary/5 via-secondary/10 to-primary/5 rounded-3xl blur-md"></div>
            <div className="relative flex items-center justify-center py-6 px-4">
              <div className="flex gap-6 items-center justify-center flex-wrap">
                <img src="/images/chrome-logo.png" alt="Chrome" className="h-16 w-16 object-contain drop-shadow-md" />
                <div className="h-10 w-px bg-muted-foreground/20"></div>
                <img src="/images/brave-logo.png" alt="Brave" className="h-16 w-16 object-contain drop-shadow-md" />
              </div>
            </div>
          </motion.div>

          <motion.div
            className="mt-8 flex flex-col sm:flex-row items-center justify-center gap-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.8 }}
          >
            {/* Chrome button with enhanced styling */}
            <div className="relative group w-full sm:w-auto">
              <div className="absolute -inset-1 bg-gradient-to-r from-primary to-secondary rounded-2xl blur opacity-70 group-hover:opacity-100 transition duration-500 group-hover:duration-200 animate-pulse-slow"></div>
              <FlamethrowerButton
                variant="default"
                size="lg"
                className="w-full relative bg-gradient-to-r from-primary to-secondary text-white shadow-xl py-7 px-10 rounded-xl transition-all duration-300 transform group-hover:scale-[1.02]"
                flameHeight={120}
                flameWidth={160}
              >
                <a href="https://chromewebstore.google.com/detail/browzyai/nfjipaniijjigbomgmnciigeikblnfmk" className="flex items-center gap-4 font-inter">
                  <div className="relative flex items-center justify-center w-8 h-8 bg-white/20 rounded-full">
                    <div className="absolute inset-0 bg-white/20 rounded-full blur-sm"></div>
                    <svg viewBox="0 0 24 24" className="w-5 h-5 relative">
                      <path fill="currentColor" d="M12 0C8.21 0 4.831 1.757 2.632 4.501l3.953 6.848A5.454 5.454 0 0 1 12 6.545h10.691A12 12 0 0 0 12 0zM1.931 5.47A11.943 11.943 0 0 0 0 12c0 6.012 4.42 10.991 10.189 11.864l3.953-6.847a5.45 5.45 0 0 1-6.865-2.29zm13.342 2.166a5.446 5.446 0 0 1 1.45 7.09l.002.003h-.002l-5.344 9.257c.206.01.413.016.621.016 6.627 0 12-5.373 12-12 0-1.54-.29-3.011-.818-4.366zM12 16.364a4.364 4.364 0 1 1 0-8.728 4.364 4.364 0 0 1 0 8.728Z" />
                    </svg>
                  </div>
                  <span className="font-semibold tracking-wide">ADD TO CHROME</span>
                  <ArrowRight className="ml-1 h-5 w-5 group-hover:translate-x-1 transition-transform duration-300" />
                </a>
              </FlamethrowerButton>
            </div>

            {/* Brave button with enhanced styling */}
            <div className="relative group w-full sm:w-auto">
              <div className="absolute -inset-1 bg-gradient-to-r from-orange-500/70 to-orange-600/50 rounded-2xl blur opacity-70 group-hover:opacity-100 transition duration-500 group-hover:duration-200"></div>
              <a
                href="https://chromewebstore.google.com/detail/browzyai/nfjipaniijjigbomgmnciigeikblnfmk"
                className="relative px-10 py-7 bg-gradient-to-r from-orange-500 to-orange-600 text-white font-inter rounded-xl hover:opacity-90 transition-all duration-300 text-lg flex items-center gap-4 w-full sm:w-auto justify-center font-semibold tracking-wide group-hover:scale-[1.02] transform shadow-xl"
              >
                <div className="relative flex items-center justify-center w-8 h-8 bg-white/20 rounded-full">
                  <div className="absolute inset-0 bg-white/20 rounded-full blur-sm"></div>
                  <img src="/images/brave-logo.png" alt="Brave" className="w-5 h-5 object-contain" />
                </div>
                ADD TO BRAVE
                <ArrowRight className="ml-1 h-5 w-5 group-hover:translate-x-1 transition-transform duration-300" />
              </a>
            </div>
          </motion.div>

          <motion.div
            className="mt-12 bg-card/40 backdrop-blur-sm p-6 rounded-xl border border-muted/30 shadow-md max-w-2xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.9 }}
          >
            <div className="flex items-start gap-3">
              <AlertTriangle className="h-5 w-5 text-secondary flex-shrink-0 mt-0.5" />
              <p className="text-muted-foreground text-sm font-inter text-left">
                <span className="font-medium text-foreground">Fair warning:</span> By installing on <span className="text-primary font-medium">Chrome</span> or <span className="text-orange-500 font-medium">Brave</span>, you agree to be judged mercilessly by an AI that's smarter than you. Our extension will analyze your browsing habits with brutal honesty and questionable tact.
              </p>
            </div>
          </motion.div>

          {/* User count */}
          <motion.div
            className="mt-10 flex items-center justify-center gap-2"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 1 }}
          >
            <div className="flex -space-x-2">
              {[1, 2, 3, 4].map((i) => (
                <div key={i} className="w-8 h-8 rounded-full bg-gradient-to-br from-primary/80 to-secondary/80 flex items-center justify-center text-white text-xs font-bold border-2 border-background">
                  {String.fromCharCode(64 + i)}
                </div>
              ))}
            </div>
            <p className="text-sm text-muted-foreground font-inter">
              <span className="font-semibold text-foreground">3.5k+</span> users already installed
            </p>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}
